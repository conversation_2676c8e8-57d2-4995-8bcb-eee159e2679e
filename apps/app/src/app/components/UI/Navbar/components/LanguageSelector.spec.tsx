import React from 'react';
import { userFactory } from '@shape-construction/api/factories/users';
import { patchApiUsersMeMockHandler } from '@shape-construction/api/handlers-factories/users';
import Dropdown from '@shape-construction/arch-ui/src/Dropdown';
import { type Locale, LocaleContext } from 'messages/LocaleProvider';
import { server, waitForRequest } from 'tests/mock-server';
import { render, screen, userEvent } from 'tests/test-utils';
import {
  LanguageSelectorRadioGroup,
  LanguageSelectorRadioItem,
  LanguageSelectorRoot,
  LanguageSelectorTrigger,
} from './LanguageSelector';

const renderWithLocaleProvider = (children: React.ReactNode, initialLocale: Locale = 'en') => {
  return render(
    <LocaleContext.Provider value={{ locale: initialLocale }}>
      <LanguageSelectorRoot>{children}</LanguageSelectorRoot>
    </LocaleContext.Provider>
  );
};

describe('LanguageSelector', () => {
  it('shows the correct label in the trigger', () => {
    renderWithLocaleProvider(<LanguageSelectorTrigger>Language</LanguageSelectorTrigger>, 'en');

    expect(screen.getByText('Language')).toBeInTheDocument();
    expect(screen.getByText('locale.options.en.label')).toBeInTheDocument();
  });

  it('renders radio items with correct labels and sublabels', async () => {
    renderWithLocaleProvider(
      <Dropdown.Root>
        <Dropdown.Trigger asChild>
          <button type="button">Open</button>
        </Dropdown.Trigger>
        <Dropdown.Items className="w-64">
          <Dropdown.Sub>
            <Dropdown.SubTrigger>Language</Dropdown.SubTrigger>
            <Dropdown.SubContentItems backLabel="Back">
              <LanguageSelectorRadioGroup>
                <LanguageSelectorRadioItem value="en" />
                <LanguageSelectorRadioItem value="es-419" />
              </LanguageSelectorRadioGroup>
            </Dropdown.SubContentItems>
          </Dropdown.Sub>
        </Dropdown.Items>
      </Dropdown.Root>,
      'en'
    );

    await userEvent.click(screen.getByRole('button', { name: 'Open' }));
    await userEvent.click(screen.getByText('Language'));

    expect(
      screen.getByRole('menuitem', { name: 'locale.options.en.label locale.options.en.subLabel' })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('menuitem', { name: 'locale.options.es-419.label locale.options.es-419.subLabel' })
    ).toBeInTheDocument();
  });

  describe('when selecting a different locale', () => {
    it('makes update user request with correct value', async () => {
      const updateUserRequest = waitForRequest('PATCH', '/api/users/me');
      server.use(patchApiUsersMeMockHandler(() => userFactory()));
      renderWithLocaleProvider(
        <Dropdown.Root>
          <Dropdown.Trigger asChild>
            <button type="button">Open</button>
          </Dropdown.Trigger>
          <Dropdown.Items className="w-64">
            <Dropdown.Sub>
              <Dropdown.SubTrigger>Language</Dropdown.SubTrigger>
              <Dropdown.SubContentItems backLabel="Back">
                <LanguageSelectorRadioGroup>
                  <LanguageSelectorRadioItem value="en" />
                  <LanguageSelectorRadioItem value="es-419" />
                </LanguageSelectorRadioGroup>
              </Dropdown.SubContentItems>
            </Dropdown.Sub>
          </Dropdown.Items>
        </Dropdown.Root>,
        'en'
      );
      await userEvent.click(screen.getByRole('button', { name: /open/i }));
      await userEvent.click(screen.getByText('Language'));

      await userEvent.click(
        screen.getByRole('menuitem', { name: 'locale.options.es-419.label locale.options.es-419.subLabel' })
      );

      const req = await updateUserRequest;
      expect(await req.json()).toEqual({
        user: {
          language: 'es-419',
        },
      });
    });
  });

  describe('when used outside LanguageSelectorRoot', () => {
    it('throws error', () => {
      expect(() => render(<LanguageSelectorTrigger>Language</LanguageSelectorTrigger>)).toThrow(
        'LanguageSelector components must be used within LanguageSelector.Root'
      );
    });
  });
});
