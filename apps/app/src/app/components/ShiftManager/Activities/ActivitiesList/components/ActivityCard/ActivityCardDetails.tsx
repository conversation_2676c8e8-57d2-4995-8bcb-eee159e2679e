import type { ShiftActivitySchema } from '@shape-construction/api/src/types';
import { ActivityBlockersBadge } from 'app/pages/projects/[projectId]/activities/components/ActivityBlockersBadge';
import { ActivityReadinessStateBadge } from 'app/pages/projects/[projectId]/activities/components/ActivityReadinessStateBadge';
import { ActivityStatusBadge } from '../../../components/ActivityStatusBadge/ActivityStatusBadge';
import { ActivityCardCritical } from './ActivityCardCritical/ActivityCardCritical';
import { ActivityCardDescription } from './ActivityCardDescription/ActivityCardDescription';
import { ActivityCardLocation } from './ActivityCardLocation/ActivityCardLocation';
import { ActivityCardProgress } from './ActivityCardProgress/ActivityCardProgress';
import { ActivityCardRef } from './ActivityCardRef/ActivityCardRef';

export interface ActivityCardDetailsProps {
  shiftActivity: ShiftActivitySchema;
}

export const ActivityCardDetails: React.FC<ActivityCardDetailsProps> = ({ shiftActivity }) => {
  const blockersCount = shiftActivity?.blockers?.blockersCount;
  const resolvedBlockersCount = shiftActivity?.blockers?.resolvedBlockersCount;
  const unresolvedBlockersCount = blockersCount - resolvedBlockersCount;
  const blockersResolved = blockersCount > 0 && unresolvedBlockersCount === 0;

  return (
    <div className="w-full flex flex-col grow gap-y-2 min-w-0">
      <div className="w-full flex flex-row items-center gap-2">
        <ActivityCardDescription description={shiftActivity.description} />
        <ActivityReadinessStateBadge shiftActivity={shiftActivity} />
        <ActivityBlockersBadge unresolvedBlockersCount={unresolvedBlockersCount} blockersResolved={blockersResolved} />
      </div>
      <div className="flex flex-row items-center gap-x-4 gap-y-2 flex-wrap">
        <ActivityCardRef referenceNumber={shiftActivity.referenceNumber} />
        <ActivityCardLocation locationId={shiftActivity.locationId} />
        <ActivityCardProgress progress={shiftActivity.percentageCompleted} />
        <ActivityStatusBadge status={shiftActivity.status} />
        <ActivityCardCritical critical={shiftActivity.critical} />
      </div>
    </div>
  );
};
