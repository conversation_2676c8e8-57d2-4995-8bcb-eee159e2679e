import React from 'react';
import { potentialChangeFactory } from '@shape-construction/api/factories/control-center';
import { projectFactory } from '@shape-construction/api/factories/projects';
import {
    getApiProjectsProjectIdMockHandler,
    getApiProjectsPotentialChangeDetailsMockHandler
} from '@shape-construction/api/handlers-factories/projects';
import {
    getApiProjectsProjectIdControlCenterPotentialChangesMockHandler,
    getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler,
    getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler
} from '@shape-construction/api/handlers-factories/projects/control-center';
import { server } from 'tests/mock-server';
import { render, screen, waitFor } from 'tests/test-utils';
import { CommercialTracker } from './CommercialTracker';

describe('<CommercialTracker />', () => {
    const project = projectFactory({ id: 'project-1' });
    const mockProps = {
        project,
        onOpenDrawer: jest.fn(),
        isSelectionMode: false,
        selectedPotentialChangeIds: undefined,
        setSelectedPotentialChangeIds: jest.fn(),
        titleRef: React.createRef<HTMLInputElement>(),
        onCreatePotentialChange: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Navigation functionality', () => {
        describe('when next button is clicked', () => {
            describe('when there is next change', () => {
                it('populates next change record', async () => {
                    const potentialChanges = [
                        potentialChangeFactory({ id: 'change-1', title: 'First Change' }),
                        potentialChangeFactory({ id: 'change-2', title: 'Second Change' }),
                        potentialChangeFactory({ id: 'change-3', title: 'Third Change' }),
                    ];
                    server.use(
                        getApiProjectsProjectIdMockHandler(() => project),
                        getApiProjectsProjectIdControlCenterPotentialChangesMockHandler(() => ({
                            entries: potentialChanges,
                            meta: {
                                total: potentialChanges.length,
                                hasNextPage: false,
                                hasPreviousPage: false,
                                lastEntryCursor: null,
                                firstEntryCursor: null,
                            },
                        })),
                        getApiProjectsPotentialChangeDetailsMockHandler(),
                        getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
                        getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler()
                    );
                    const { user } = render(<CommercialTracker {...mockProps} />);
                    await waitFor(() => {
                        expect(screen.getByDisplayValue('First Change')).toBeInTheDocument();
                    });
                    const detailsButton = await screen.findAllByRole('button', { name: 'view details' });
                    await user.click(detailsButton[0]);
                    const nextButton = await screen.findByLabelText(
                        'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.next'
                    );
                    await user.click(nextButton);
                    await waitFor(() => {
                        expect(screen.getByDisplayValue('Second Change')).toBeInTheDocument();
                    });
                });
            });

            describe.skip('when there is no next change', () => {
                describe('when there are no next pages', () => {
                    it('disables next button', async () => {
                        const potentialChanges = [
                            potentialChangeFactory({ id: 'change-1', title: 'Only Change' }),
                        ];

                        server.use(
                            getApiProjectsProjectIdMockHandler(() => project),
                            getApiProjectsProjectIdControlCenterPotentialChangesMockHandler(() => ({
                                entries: potentialChanges,
                                meta: {
                                    total: potentialChanges.length,
                                    hasNextPage: false,
                                    hasPreviousPage: false,
                                    lastEntryCursor: null,
                                    firstEntryCursor: null,
                                },
                            })),
                            getApiProjectsPotentialChangeDetailsMockHandler(),
                            getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
                            getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler()
                        );

                        const { user } = render(<CommercialTracker {...mockProps} />);

                        // Wait for data to load
                        await waitFor(() => {
                            expect(screen.getByText('Only Change')).toBeInTheDocument();
                        });

                        // Click on the change to open drawer
                        await user.click(screen.getByText('Only Change'));

                        // Verify next button is disabled
                        const nextButton = await screen.findByLabelText(
                            'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.next'
                        );
                        expect(nextButton).toBeDisabled();
                    });
                });

                describe('when there are next pages', () => {
                    it('fetches next page and populates next change record', async () => {
                        const firstPageChanges = [
                            potentialChangeFactory({ id: 'change-1', title: 'First Change' }),
                            potentialChangeFactory({ id: 'change-2', title: 'Second Change' }),
                        ];

                        const secondPageChanges = [
                            potentialChangeFactory({ id: 'change-3', title: 'Third Change' }),
                        ];

                        let callCount = 0;
                        server.use(
                            getApiProjectsProjectIdMockHandler(() => project),
                            getApiProjectsProjectIdControlCenterPotentialChangesMockHandler((info) => {
                                const url = new URL(info.request.url);
                                const after = url.searchParams.get('after');

                                if (!after && callCount === 0) {
                                    callCount++;
                                    return {
                                        entries: firstPageChanges,
                                        meta: {
                                            total: 3,
                                            hasNextPage: true,
                                            hasPreviousPage: false,
                                            lastEntryCursor: 'cursor-1',
                                            firstEntryCursor: null,
                                        },
                                    };
                                }

                                return {
                                    entries: [...firstPageChanges, ...secondPageChanges],
                                    meta: {
                                        total: 3,
                                        hasNextPage: false,
                                        hasPreviousPage: false,
                                        lastEntryCursor: null,
                                        firstEntryCursor: null,
                                    },
                                };
                            }),
                            getApiProjectsPotentialChangeDetailsMockHandler(),
                            getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
                            getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler()
                        );

                        const { user } = render(<CommercialTracker {...mockProps} />);

                        // Wait for data to load
                        await waitFor(() => {
                            expect(screen.getByText('First Change')).toBeInTheDocument();
                        });

                        // Click on second change (last in current page) to open drawer
                        await user.click(screen.getByText('Second Change'));

                        // Find and click next button to trigger page fetch
                        const nextButton = await screen.findByLabelText(
                            'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.next'
                        );
                        await user.click(nextButton);

                        // Verify third change from next page is now selected
                        await waitFor(() => {
                            expect(screen.getByText('Third Change')).toBeInTheDocument();
                        });
                    });
                });
            });
        });

        describe.skip('when previous button is clicked', () => {
            it('populates previous change record', async () => {
                const potentialChanges = [
                    potentialChangeFactory({ id: 'change-1', title: 'First Change' }),
                    potentialChangeFactory({ id: 'change-2', title: 'Second Change' }),
                    potentialChangeFactory({ id: 'change-3', title: 'Third Change' }),
                ];

                server.use(
                    getApiProjectsProjectIdMockHandler(() => project),
                    getApiProjectsProjectIdControlCenterPotentialChangesMockHandler(() => ({
                        entries: potentialChanges,
                        meta: {
                            total: potentialChanges.length,
                            hasNextPage: false,
                            hasPreviousPage: false,
                            lastEntryCursor: null,
                            firstEntryCursor: null,
                        },
                    })),
                    getApiProjectsPotentialChangeDetailsMockHandler(),
                    getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
                    getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler()
                );

                const { user } = render(<CommercialTracker {...mockProps} />);

                // Wait for data to load
                await waitFor(() => {
                    expect(screen.getByText('Second Change')).toBeInTheDocument();
                });

                // Click on second change to open drawer
                await user.click(screen.getByText('Second Change'));

                // Find and click previous button
                const previousButton = await screen.findByLabelText(
                    'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.previous'
                );
                await user.click(previousButton);

                // Verify first change is now selected
                await waitFor(() => {
                    expect(screen.getByText('First Change')).toBeInTheDocument();
                });
            });

            it('disables previous button when at first change', async () => {
                const potentialChanges = [
                    potentialChangeFactory({ id: 'change-1', title: 'First Change' }),
                    potentialChangeFactory({ id: 'change-2', title: 'Second Change' }),
                ];

                server.use(
                    getApiProjectsProjectIdMockHandler(() => project),
                    getApiProjectsProjectIdControlCenterPotentialChangesMockHandler(() => ({
                        entries: potentialChanges,
                        meta: {
                            total: potentialChanges.length,
                            hasNextPage: false,
                            hasPreviousPage: false,
                            lastEntryCursor: null,
                            firstEntryCursor: null,
                        },
                    })),
                    getApiProjectsPotentialChangeDetailsMockHandler(),
                    getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
                    getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler()
                );

                const { user } = render(<CommercialTracker {...mockProps} />);

                // Wait for data to load
                await waitFor(() => {
                    expect(screen.getByText('First Change')).toBeInTheDocument();
                });

                // Click on first change to open drawer
                await user.click(screen.getByText('First Change'));

                // Verify previous button is disabled
                const previousButton = await screen.findByLabelText(
                    'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.previous'
                );
                expect(previousButton).toBeDisabled();
            });
        });

        describe.skip('when fetchNextPage fails', () => {
            it('captures error with Sentry', async () => {
                const sentrySpy = jest.spyOn(require('@sentry/react'), 'captureException');

                const potentialChanges = [
                    potentialChangeFactory({ id: 'change-1', title: 'First Change' }),
                ];

                server.use(
                    getApiProjectsProjectIdMockHandler(() => project),
                    getApiProjectsProjectIdControlCenterPotentialChangesMockHandler((info) => {
                        const url = new URL(info.request.url);
                        const after = url.searchParams.get('after');

                        if (!after) {
                            return {
                                entries: potentialChanges,
                                meta: {
                                    total: 2,
                                    hasNextPage: true,
                                    hasPreviousPage: false,
                                    lastEntryCursor: 'cursor-1',
                                    firstEntryCursor: null,
                                },
                            };
                        }

                        // Simulate error on next page fetch
                        throw new Error('Network error');
                    }),
                    getApiProjectsPotentialChangeDetailsMockHandler(),
                    getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
                    getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler()
                );

                const { user } = render(<CommercialTracker {...mockProps} />);

                // Wait for data to load
                await waitFor(() => {
                    expect(screen.getByText('First Change')).toBeInTheDocument();
                });

                // Click on first change to open drawer
                await user.click(screen.getByText('First Change'));

                // Find and click next button to trigger error
                const nextButton = await screen.findByLabelText(
                    'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.next'
                );
                await user.click(nextButton);

                // Verify error was captured
                await waitFor(() => {
                    expect(sentrySpy).toHaveBeenCalledWith(expect.any(Error));
                });

                sentrySpy.mockRestore();
            });
        });
    });

    describe.skip('Empty state rendering', () => {
        describe('when there are no potential changes', () => {
            describe('when not in selection mode', () => {
                it('renders default empty state', async () => {
                    server.use(
                        getApiProjectsProjectIdMockHandler(() => project),
                        getApiProjectsProjectIdControlCenterPotentialChangesMockHandler(() => ({
                            entries: [],
                            meta: {
                                total: 0,
                                hasNextPage: false,
                                hasPreviousPage: false,
                                lastEntryCursor: null,
                                firstEntryCursor: null,
                            },
                        }))
                    );

                    render(<CommercialTracker {...mockProps} />);

                    await waitFor(() => {
                        expect(screen.getByText('controlCenter.commercialTracker.emptyStatePotentialChanges.browseSignalsCTA')).toBeInTheDocument();
                    });
                });
            });

            describe('when in selection mode', () => {
                it('renders selection mode empty state', async () => {
                    server.use(
                        getApiProjectsProjectIdMockHandler(() => project),
                        getApiProjectsProjectIdControlCenterPotentialChangesMockHandler(() => ({
                            entries: [],
                            meta: {
                                total: 0,
                                hasNextPage: false,
                                hasPreviousPage: false,
                                lastEntryCursor: null,
                                firstEntryCursor: null,
                            },
                        }))
                    );

                    render(<CommercialTracker {...mockProps} isSelectionMode={true} />);

                    await waitFor(() => {
                        expect(screen.getByText('controlCenter.commercialTracker.emptyStatePotentialChangesSelectionMode.title')).toBeInTheDocument();
                    });
                });
            });
        });
    });

    describe.skip('Component rendering', () => {
        it('renders change signals summary', async () => {
            const potentialChanges = [
                potentialChangeFactory({ id: 'change-1', title: 'Test Change' }),
            ];

            server.use(
                getApiProjectsProjectIdMockHandler(() => project),
                getApiProjectsProjectIdControlCenterPotentialChangesMockHandler(() => ({
                    entries: potentialChanges,
                    meta: {
                        total: potentialChanges.length,
                        hasNextPage: false,
                        hasPreviousPage: false,
                        lastEntryCursor: null,
                        firstEntryCursor: null,
                    },
                }))
            );

            render(<CommercialTracker {...mockProps} />);

            await waitFor(() => {
                expect(screen.getByText('Test Change')).toBeInTheDocument();
            });
        });

        it('renders potential changes table when data is available', async () => {
            const potentialChanges = [
                potentialChangeFactory({ id: 'change-1', title: 'First Change' }),
                potentialChangeFactory({ id: 'change-2', title: 'Second Change' }),
            ];

            server.use(
                getApiProjectsProjectIdMockHandler(() => project),
                getApiProjectsProjectIdControlCenterPotentialChangesMockHandler(() => ({
                    entries: potentialChanges,
                    meta: {
                        total: potentialChanges.length,
                        hasNextPage: false,
                        hasPreviousPage: false,
                        lastEntryCursor: null,
                        firstEntryCursor: null,
                    },
                }))
            );

            render(<CommercialTracker {...mockProps} />);

            await waitFor(() => {
                expect(screen.getByText('First Change')).toBeInTheDocument();
                expect(screen.getByText('Second Change')).toBeInTheDocument();
            });
        });

        it('passes correct props to PotentialChangesTable', async () => {
            const potentialChanges = [
                potentialChangeFactory({ id: 'change-1', title: 'Test Change' }),
            ];

            server.use(
                getApiProjectsProjectIdMockHandler(() => project),
                getApiProjectsProjectIdControlCenterPotentialChangesMockHandler(() => ({
                    entries: potentialChanges,
                    meta: {
                        total: potentialChanges.length,
                        hasNextPage: true,
                        hasPreviousPage: false,
                        lastEntryCursor: 'cursor-1',
                        firstEntryCursor: null,
                    },
                }))
            );

            render(<CommercialTracker {...mockProps} newRecordId="new-record-123" />);

            await waitFor(() => {
                expect(screen.getByText('Test Change')).toBeInTheDocument();
            });

            // Verify that infinite scroll waypoint is rendered (indicates hasNextPage is passed correctly)
            expect(screen.getByTestId('top-waypoint')).toBeInTheDocument();
        });
    });

    describe.skip('Callback functions', () => {
        it('calls onOpenDrawer when browse signals is clicked in empty state', async () => {
            const onOpenDrawer = jest.fn();

            server.use(
                getApiProjectsProjectIdMockHandler(() => project),
                getApiProjectsProjectIdControlCenterPotentialChangesMockHandler(() => ({
                    entries: [],
                    meta: {
                        total: 0,
                        hasNextPage: false,
                        hasPreviousPage: false,
                        lastEntryCursor: null,
                        firstEntryCursor: null,
                    },
                }))
            );

            const { user } = render(<CommercialTracker {...mockProps} onOpenDrawer={onOpenDrawer} />);

            await waitFor(() => {
                expect(screen.getByText('controlCenter.commercialTracker.emptyStatePotentialChanges.browseSignalsCTA')).toBeInTheDocument();
            });

            await user.click(screen.getByText('controlCenter.commercialTracker.emptyStatePotentialChanges.browseSignalsCTA'));

            expect(onOpenDrawer).toHaveBeenCalled();
        });

        it('calls onCreatePotentialChange when create new change is clicked', async () => {
            const onCreatePotentialChange = jest.fn();

            server.use(
                getApiProjectsProjectIdMockHandler(() => project),
                getApiProjectsProjectIdControlCenterPotentialChangesMockHandler(() => ({
                    entries: [],
                    meta: {
                        total: 0,
                        hasNextPage: false,
                        hasPreviousPage: false,
                        lastEntryCursor: null,
                        firstEntryCursor: null,
                    },
                }))
            );

            const { user } = render(<CommercialTracker {...mockProps} onCreatePotentialChange={onCreatePotentialChange} />);

            await waitFor(() => {
                expect(screen.getByText('controlCenter.commercialTracker.emptyStatePotentialChanges.createNewChangeCTA')).toBeInTheDocument();
            });

            await user.click(screen.getByText('controlCenter.commercialTracker.emptyStatePotentialChanges.createNewChangeCTA'));

            expect(onCreatePotentialChange).toHaveBeenCalled();
        });
    });
});
