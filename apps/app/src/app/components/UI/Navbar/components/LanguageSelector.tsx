import { createContext, type ReactNode, useContext, useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Dropdown from '@shape-construction/arch-ui/src/Dropdown';
import { useUpdateUser } from 'app/queries/users/users';
import { type Locale, LocaleContext } from 'messages/LocaleProvider';

export type LocaleOption = {
  value: string;
  label: string;
  subLabel: string;
};
type LanguageSelectorContextType = {
  locale: Locale;
  localeOptions: LocaleOption[];
  localeLabel?: string;
};

export const LanguageSelectorContext = createContext<LanguageSelectorContextType | null>(null);

const useLanguageSelector = () => {
  const context = useContext(LanguageSelectorContext);
  if (!context) {
    throw new Error('LanguageSelector components must be used within LanguageSelector.Root');
  }
  return context;
};

export const LanguageSelectorRoot: React.FC<{
  children: ReactNode;
}> = ({ children }) => {
  const messages = useMessageGetter('locale.options');

  const { locale } = useContext(LocaleContext);
  const localeOptions: LocaleOption[] = [
    { value: 'en', label: messages('en.label'), subLabel: messages('en.subLabel') },
    { value: 'es-419', label: messages('es-419.label'), subLabel: messages('es-419.subLabel') },
  ];
  const localeLabel = localeOptions?.find((option) => option.value === locale)?.label;

  const contextValue = useMemo(
    () => ({
      locale,
      localeOptions,
      localeLabel,
    }),
    [locale, localeOptions, localeLabel]
  );
  return <LanguageSelectorContext.Provider value={contextValue}>{children}</LanguageSelectorContext.Provider>;
};

export const LanguageSelectorTrigger: React.FC<{
  children: ReactNode;
}> = ({ children }) => {
  const { localeLabel } = useLanguageSelector();

  return (
    <div className="flex flex-col">
      {children}
      <span className="text-sm leading-5 font-normal text-neutral-subtle">{localeLabel}</span>
    </div>
  );
};

export const LanguageSelectorRadioGroup: React.FC<{
  children: ReactNode;
}> = ({ children }) => {
  const { locale } = useLanguageSelector();
  const { mutate: updateUser } = useUpdateUser();

  const submitLanguageChange = (value: string) => {
    updateUser({
      data: {
        user: {
          language: value,
        },
      },
    });
  };

  return (
    <Dropdown.RadioGroup value={locale} onValueChange={submitLanguageChange}>
      {children}
    </Dropdown.RadioGroup>
  );
};

export const LanguageSelectorRadioItem: React.FC<{
  value: string;
}> = ({ value }) => {
  const { localeOptions } = useLanguageSelector();
  const option = localeOptions.find((opt) => opt.value === value);

  if (!option) return null;

  return (
    <Dropdown.RadioItem key={value} value={value}>
      <div className="flex flex-col">
        <span>{option.label}</span>
        <span className="text-xs leading-4 font-normal text-neutral-subtle">{option.subLabel}</span>
      </div>
    </Dropdown.RadioItem>
  );
};
