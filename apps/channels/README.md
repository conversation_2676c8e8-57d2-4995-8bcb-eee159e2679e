# Channels 
 
Welcome to the Shape Channels mobile application.

## Prerequisites

Before you can run the project locally, ensure that you have the following prerequisites installed on your machine:

1. **Install Java 17:**

   `brew install --cask zulu@17`

2. **Android Studio (for Android development):**

   If you plan to run the project on an Android simulator, you'll need to have Android Studio installed. Follow the steps below to install Android Studio:

   - **Download Android Studio:**
     Visit the [Android Studio website](https://developer.android.com/studio) and download the latest version of Android Studio for your operating system (Windows, macOS, or Linux).

   - **Install Android Studio:**
     Follow the installation instructions provided on the website for your specific operating system.

   - **Configure Android Studio:**
     After installation, open Android Studio and follow the setup wizard to install the Android SDK, emulator, and other necessary components.

     - **Install the latest Android SDK:**
       During the Android Studio setup, make sure to install the latest Android SDK. This is essential for building and running Android applications.

     - **Configure the Android Emulator:**
       After installing the Android SDK, open the Android Studio IDE, and click on "Configure" in the welcome screen or go to "File" > "Settings" (on Windows/Linux) or "Android Studio" > "Settings" (on macOS).

       - In the settings, navigate to "Languages and Framework" > "Android SDK."
       - Ensure that the necessary Android API levels are installed. If not, click on the "SDK Platforms" tab, select the desired API levels, and click "Apply" to install them.

       - Next, navigate to the "SDK Tools" tab and check "Android Emulator" to install the emulator components.

       - Open the Virtual Device Manager on the "More Actions" button in order to select an android virtual device to run.

     - **Create a Virtual Device (Emulator):**
       In the AVD Manager, click on "Create Virtual Device." Choose a hardware profile that matches your development machine specifications (e.g., Pixel 3), and select a system image for the desired API level.

       - Click "Next" and configure additional settings such as RAM, VM heap, and orientation.
       - Click "Finish" to create the virtual device.

     - **Run the Virtual Device:**
       In the AVD Manager, click the green play button next to the created virtual device to start the emulator.

   Now, you have Android Studio configured with the necessary components, and you can run the project on the Android emulator.

3. **Xcode (for iOS development):**

   If you plan to run the project on an iOS simulator, you'll need to have Xcode installed on a Mac. Follow these steps to install Xcode:

   - **Install Xcode from the App Store:**

     - Open the App Store on your Mac.
     - Search for "Xcode" in the App Store search bar.
     - Click on the "Get" button next to Xcode to download and install it.

   - **Configure Xcode Command Line Tools:**

     - Open Xcode and navigate to "Xcode" > "Preferences" in the top menu.
     - Go to the "Locations" tab.
     - Ensure that the "Command Line Tools" dropdown is set to the latest version.

   - **Install iOS Simulator:**

     - Open Xcode and navigate to "Xcode" > "Settings."
     - Go to the "Components" tab.
     - Under "Simulator," click "Download" next to the desired iOS simulator version.

   - **Run the iOS Simulator:**
     - Search and open the "Simulator" application.

4. **Node.js and npm:** Shape Channels require **Node.js** version `20.9.0` and **pnpm** version `9.0.4`.

## Development - Getting Started

In order to test your local developments and to see how they appear on a mobile application you have to:

1. **Clone the repository:**

   ```bash
   <NAME_EMAIL>:shape-construction/channels.git
   cd channels
   ```

2. **Install dependencies:**

   ```bash
   pnpm install
   ```

3. **Run local development build:**

   - **For Android:**

     ```bash
     pnpm run android
     ```

     **_Note_**: in Android, the emulator localhost is not shared with your machine. To expose `localhost:3001` inside the emulator (or real device), you can use `adb reverse tcp:3001 tcp:3001`. Alternatively, you can also change `EXPO_PUBLIC_API_URL` to `http://********:3001`. As an example, you can create a `.env.local` with the following: `EXPO_PUBLIC_API_URL=http://********:3001`

     <br>

   - **For iOS:**

     ```bash
     pnpm run ios
     ```

## Continuous Integration and Application Submition - Expo Application Services

EAS is a set of tools and services provided by Expo for building and deploying React Native applications more efficiently.

Key features and services that Expo Application Services may include are:

1. **Build Service**: EAS provides a cloud-based build service that allows developers to build their React Native applications in the cloud. This can be particularly useful for large projects or projects with complex dependencies, as it offloads the build process to Expo's infrastructure.

2. **Submit Service**: EAS may include a service for submitting and managing app submissions to app stores (such as the Apple App Store and Google Play Store). This can help automate the process of submitting and updating apps, reducing the manual steps required.

3. **Credentials Management**: Expo Application Services may offer a secure and centralized way to manage credentials, keys, and other sensitive information needed for building and deploying applications. This can enhance security and simplify the management of deployment-related configurations.

4. **Monitoring and Analytics**: EAS might include tools for monitoring and analyzing the performance of your React Native applications. This can help developers identify issues, track user behavior, and optimize app performance.

### Channels on EAS

We use Expo Application Services to manage our application distribution. Currently we only use this for testing purposes either for pull request testing (like a review app on the issue tracker and shift report) or a staging build.

In the future we will have this integration more well defined and with better integration with EAS services.

The URL for our EAS project: https://expo.dev/accounts/shapeconstruction/projects/channels

## Development and EAS configuration

### Local development

Regarding local development configuration we are using a local `.env` file in order to manage things like our API endpoint.

Example:

```
EXPO_PUBLIC_API_URL=http://localhost:3001
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID= ************-keanutoauujrg1pbos6t8h365me4orlv.apps.googleusercontent.com
************-9oqntsr40l4d4cn4bks7d4vtmsf91d0o
```

### EAS services

We have three concepts that we need to configure in order to use EAS services:

- Environments/Profiles.
- Channels.
- Branches.

You can find more about this here: https://docs.expo.dev/build/eas-json/, https://docs.expo.dev/eas/json/

This configuration is mapped in the `eas.json` file:

```json
{
  "cli": {
    "version": ">= 5.9.1",
    "appVersionSource": "remote"
  },
  "build": {
    "base": {
      "node": "v22.18.0",
      "pnpm": "9.0.4"
    },
    "production": {
      "extends": "base",
      "distribution": "store",
      "channel": "production",
      "env": {
        "EXPO_PUBLIC_API_URL": "https: //api.shape.construction"
      },
      "autoIncrement": true
    },
    "staging": {
      "extends": "base",
      "distribution": "internal",
      "channel": "staging",
      "env": {
        "EXPO_PUBLIC_API_URL": "https://api.shape-staging.construction",
        "EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID": "889797786373-rjbpqq5tjjg49qr70tdlmop6dj424p11.apps.googleusercontent.com"
      }
    }
  },
  "submit": {
    "ios": {},
    "android": {}
  }
}
```

## Generate theme

Access to the [Arch UI Foundation](https://www.figma.com/design/AE4q9Vm06nS2jTxGY3LHRZ/%F0%9F%93%9A-Arch-UI---Foundation?node-id=5167-177) in figma and with the developer mode enable, find the plugin `variables2css`.
First, choose the `Primitive: Color` collection, generate and copy the content to the file `colors-primitives.json`. Then do the same for the `Semantic: Color` collection and paste the content in the `color-tokens.json` file.
Finally, just run the command `pnpm run generate:theme`.
