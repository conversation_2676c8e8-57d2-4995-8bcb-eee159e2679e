import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar/Avatar';
import Divider from '@shape-construction/arch-ui/src/Divider';
import Dropdown from '@shape-construction/arch-ui/src/Dropdown';
import { ChevronDownIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import {
  ArrowDownTrayIcon,
  ArrowRightOnRectangleIcon,
  ChatBubbleLeftIcon,
  ChevronRightIcon,
  LanguageIcon,
  RectangleGroupIcon,
  UserCircleIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';
import { useModal } from '@shape-construction/hooks';
import { environment } from 'app/config/environment';
import { useInstallApp } from 'app/hooks/useInstallApp';
import { useCurrentUser, useLogout } from 'app/queries/users/users';
import { Link } from 'react-router';
import { FeedbackForm } from './FeedbackForm';
import {
  LanguageSelectorRadioGroup,
  LanguageSelectorRadioItem,
  LanguageSelectorRoot,
  LanguageSelectorTrigger,
} from './LanguageSelector';

type BaseMenuItem = {
  icon: React.FC<React.ComponentProps<'svg'>>;
  label: string;
  hasDivider?: boolean;
  isHidden?: boolean;
  dataCy?: string;
};

type RouteMenuItem = BaseMenuItem & {
  route: string;
  onClick?: never;
  renderSubMenu?: never;
};

type ActionMenuItem = BaseMenuItem & {
  onClick: () => void;
  route?: never;
  renderSubMenu?: never;
};

type SubMenuItem = BaseMenuItem & {
  renderSubMenu: () => React.ReactNode;
  route?: never;
  onClick?: never;
};

type ProfileMenuItemType = RouteMenuItem | ActionMenuItem | SubMenuItem;

const ProfileMenuSeparator = () => (
  <div className="py-2">
    <Divider orientation="horizontal" />
  </div>
);

const ProfileMenuItem: React.FC<{ item: ProfileMenuItemType }> = ({ item }) => {
  const { icon, label, hasDivider, isHidden, dataCy, route, onClick, renderSubMenu } = item;
  if (isHidden) return null;

  const content = () => {
    if (renderSubMenu) {
      return renderSubMenu();
    }

    if (route) {
      return (
        <Link to={route} data-cy={dataCy}>
          <Dropdown.Item
            icon={icon}
            className="cursor-pointer font-semibold [&>svg]:size-5 [&>svg]:text-icon-neutral-subtle"
          >
            {label}
          </Dropdown.Item>
        </Link>
      );
    }

    return (
      <Dropdown.Item
        icon={icon}
        onClick={onClick}
        data-cy={dataCy}
        className="cursor-pointer font-semibold [&>svg]:size-5 [&>svg]:text-icon-neutral-subtle"
      >
        {label}
      </Dropdown.Item>
    );
  };

  return (
    <>
      {content()}
      {hasDivider && <ProfileMenuSeparator />}
    </>
  );
};

export const ProfileMenu: React.FC = () => {
  const isLocaleSelectorEnabled = environment.LOCALE_SELECTOR;
  const messages = useMessageGetter('profileMenu');
  const backLabel = useMessageGetter('navigation');

  const { avatarUrl, email, name } = useCurrentUser();
  const appVersion = process.env.REACT_APP_VERSION;

  const { mutate: logout } = useLogout();
  const { isInstallSupported, isInStandaloneMode, promptToInstall } = useInstallApp();
  const hideInstallButton = !isInstallSupported || isInStandaloneMode;

  const {
    open: isFeedbackDialogOpen,
    openModal: openFeedbackDialog,
    closeModal: closeFeedbackDialog,
  } = useModal(false);

  const profileMenuItems: ProfileMenuItemType[] = [
    {
      icon: UserCircleIcon,
      label: messages('menu.myAccount'),
      route: 'my-profile',
      dataCy: 'res-profile-menu-my-account-link',
    },
    {
      icon: RectangleGroupIcon,
      label: messages('menu.myProjects'),
      route: 'my-projects',
      dataCy: 'res-profile-menu-my-projects-link',
      hasDivider: !isLocaleSelectorEnabled,
    },
    {
      icon: LanguageIcon,
      label: messages('menu.language'),
      hasDivider: true,
      isHidden: !isLocaleSelectorEnabled,
      renderSubMenu: () => (
        <LanguageSelectorRoot>
          <Dropdown.Sub>
            <Dropdown.SubTrigger
              icon={LanguageIcon}
              endAdornment={<ChevronRightIcon className="size-4 text-icon-neutral-subtle" />}
              className="cursor-pointer font-semibold [&>svg]:size-5 [&>svg]:text-icon-neutral-subtle"
            >
              <LanguageSelectorTrigger>{messages('menu.language')}</LanguageSelectorTrigger>
            </Dropdown.SubTrigger>
            <Dropdown.SubContentItems backLabel={backLabel('back')}>
              <LanguageSelectorRadioGroup>
                <LanguageSelectorRadioItem value="en" />
                <LanguageSelectorRadioItem value="es-419" />
              </LanguageSelectorRadioGroup>
            </Dropdown.SubContentItems>
          </Dropdown.Sub>
        </LanguageSelectorRoot>
      ),
    },
    {
      icon: ArrowDownTrayIcon,
      label: messages('menu.installApp'),
      isHidden: hideInstallButton,
      onClick: promptToInstall,
    },
    {
      icon: ChatBubbleLeftIcon,
      label: messages('menu.sendFeedback'),
      onClick: openFeedbackDialog,
      dataCy: 'profile-menu-send-feedback',
    },
    {
      icon: ArrowRightOnRectangleIcon,
      label: messages('menu.logout'),
      onClick: logout,
      dataCy: 'res-profile-menu-logout-link',
    },
  ];

  return (
    <>
      <Dropdown.Root>
        <Dropdown.Trigger asChild>
          <button
            type="button"
            className="inline-flex items-center gap-x-3 rounded-full py-2 px-1"
            data-cy="res-my-profile-link"
          >
            <Avatar text={name} imgURL={avatarUrl ?? ''} size="sm" />
            <span className="hidden items-center gap-x-1 text-sm font-medium leading-5 md:flex">
              {name} <ChevronDownIcon className="size-4 text-icon-neutral-subtle" />
            </span>
          </button>
        </Dropdown.Trigger>
        <Dropdown.Items className="w-72">
          <Dropdown.Item className="pointer-events-none overflow-x-hidden [&>span]:w-full [&>span]:pt-2">
            {messages('signedInAs')}
            <div className="font-medium truncate w-full">{email ?? name}</div>
          </Dropdown.Item>
          <ProfileMenuSeparator />
          {profileMenuItems.map((item) => (
            <ProfileMenuItem key={item.label} item={item} />
          ))}
          <ProfileMenuSeparator />
          <Dropdown.Item disabled className="[&>span]:pb-2 [&>span]:text-xs">
            {messages('appVersion', { version: appVersion })}
          </Dropdown.Item>
        </Dropdown.Items>
      </Dropdown.Root>
      <FeedbackForm open={isFeedbackDialogOpen} onClose={closeFeedbackDialog} />
    </>
  );
};
