{"name": "channels", "main": "src/index.js", "private": true, "packageManager": "pnpm@9.0.4", "engines": {"node": "v22.18.0", "pnpm": "9.0.4"}, "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "compile": "tsc", "test": "jest", "test:ci": "jest --ci --coverage --forceExit --detect<PERSON><PERSON>Handles", "lint": "biome check .", "lint:autofix": "biome check --write .", "format": "biome format --write", "prebuild": "pnpm dlx expo prebuild", "prebuild:clean": "pnpm dlx expo prebuild --clean", "build:review:ios": "pnpm dlx eas-cli build --profile review --platform ios --non-interactive", "build:review:android": "pnpm dlx eas-cli build --profile review --platform android --non-interactive", "build:staging:ios": "pnpm dlx eas-cli build --profile staging --platform ios --non-interactive", "build:staging:android": "pnpm dlx eas-cli build --profile staging --platform android --non-interactive", "build:production:ios": "pnpm dlx eas-cli build --profile production --platform ios --non-interactive", "build:production:android": "pnpm dlx eas-cli build --profile production --platform android --non-interactive", "submit:staging:android": "pnpm dlx eas-cli submit --platform android --profile staging --latest --non-interactive", "submit:staging:ios": "pnpm dlx eas-cli submit --platform ios --profile staging --latest --non-interactive", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@expo/html-elements": "^0.10.1", "@expo/react-native-action-sheet": "^4.1.0", "@gorhom/bottom-sheet": "5.0.4", "@gorhom/portal": "1.0.14", "@hookform/resolvers": "3.9.0", "@lodev09/react-native-exify": "0.2.7", "@notifee/react-native": "9.1.2", "@op-engineering/op-sqlite": "14.0.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2", "@react-hook/debounce": "4.0.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "11.4.1", "@react-native-firebase/app": "21.2.0", "@react-native-firebase/messaging": "21.2.0", "@react-native-google-signin/google-signin": "13.1.0", "@sentry/react-native": "6.10.0", "@shape-construction/api": "workspace:*", "@shape-construction/arch-ui-native": "workspace:*", "@shape-construction/utils": "workspace:*", "@tanstack/query-async-storage-persister": "5.59.13", "@tanstack/react-query": "5.52.1", "@tanstack/react-query-persist-client": "5.52.1", "@testing-library/jest-native": "5.4.3", "@testing-library/react-native": "12.8.1", "@types/lodash.groupby": "4.6.9", "axios": "1.10.0", "buffer": "6.0.3", "date-fns": "4.1.0", "date-fns-tz": "3.2.0", "dotenv": "17.2.0", "expo": "52.0.46", "expo-auth-session": "6.0.3", "expo-background-fetch": "13.0.6", "expo-build-properties": "0.13.2", "expo-clipboard": "7.0.1", "expo-constants": "17.0.8", "expo-crypto": "14.0.2", "expo-dev-client": "5.0.20", "expo-device": "~7.0.3", "expo-document-picker": "13.0.3", "expo-file-system": "18.0.12", "expo-image-manipulator": "13.0.6", "expo-image-picker": "16.0.6", "expo-linking": "7.0.5", "expo-location": "~18.0.10", "expo-media-library": "17.0.6", "expo-router": "^4.0.20", "expo-secure-store": "14.0.1", "expo-sharing": "13.0.1", "expo-splash-screen": "0.29.22", "expo-status-bar": "2.0.1", "expo-system-ui": "4.0.9", "expo-updates": "0.27.4", "expo-video": "~2.0.6", "expo-web-browser": "14.0.2", "i18next": "23.16.3", "lodash.capitalize": "^4.2.1", "lodash.get": "4.4.2", "lodash.groupby": "4.6.0", "msw": "2.7.0", "nativewind": "4.1.23", "react": "18.3.1", "react-hook-form": "7.53.0", "react-i18next": "15.0.3", "react-native": "0.76.9", "react-native-compressor": "^1.11.0", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "2.20.2", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "3.16.7", "react-native-root-toast": "3.6.0", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.11.2", "react-native-uuid": "2.0.2", "stream-chat": "9.6.0", "stream-chat-expo": "7.1.1", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "7.28.0", "@biomejs/biome": "2.0.6", "@react-native/babel-preset": "0.73.21", "@svgr/plugin-jsx": "8.1.0", "@svgr/plugin-svgo": "8.1.0", "@types/jest": "29.4.0", "@types/lodash.capitalize": "^4.2.9", "@types/lodash.get": "^4.4.9", "@types/react": "18.0.28", "@types/url-parse": "1.4.11", "babel-preset-expo": "^12.0.11", "jest": "29.7.0", "jest-expo": "~52.0.6", "react-dom": "18.3.1", "tailwindcss": "3.4.14", "typescript": "5.8.3"}}