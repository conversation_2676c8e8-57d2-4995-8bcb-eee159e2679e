import { createContext, type ReactNode, useEffect, useMemo, useState } from 'react';
import { MessageProvider } from '@messageformat/react';
import * as Sentry from '@sentry/react';
import type { UserLanguageSchema } from '@shape-construction/api/src/types';
import { environment } from 'app/config/environment';
import { useUsersMe } from 'app/queries/users/users';
import enMessages from '../messages/messages.en.yaml';
import { loadMessages } from './loadMessages';

export type Locale = UserLanguageSchema;
type MessageObject = Record<string, any>;
type LocaleMessages = Record<Locale, MessageObject>;
export type LocaleContextType = {
  locale: Locale;
};

const SUPPORTED_LOCALES: Locale[] = ['en', 'es-419'];
const DEFAULT_LOCALE: Locale = 'en';

const detectLocale = (): Locale => {
  const browserLang = navigator.language;

  if (browserLang?.startsWith('es') && !['es', 'es-ES'].includes(browserLang)) {
    return 'es-419';
  }

  return SUPPORTED_LOCALES.includes(browserLang as Locale) ? (browserLang as Locale) : DEFAULT_LOCALE;
};

export const LocaleContext = createContext<LocaleContextType>({
  locale: DEFAULT_LOCALE,
});

type LocaleProviderProps = {
  readonly children: ReactNode;
};

export const LocaleProvider = ({ children }: LocaleProviderProps) => {
  const isLocaleSelectorEnabled = environment.LOCALE_SELECTOR;
  const { data: user, isLoading: isUserLoading } = useUsersMe();
  const [messages, setMessages] = useState<LocaleMessages>({
    en: enMessages,
  } as LocaleMessages);
  const [isLoading, setIsLoading] = useState(true);

  const locale: Locale = useMemo(() => {
    if (!isLocaleSelectorEnabled) return DEFAULT_LOCALE;

    if (user?.language) return user.language;
    return detectLocale();
  }, [user, isLocaleSelectorEnabled]);

  const localeMessages = useMemo(() => {
    if (isLocaleSelectorEnabled) return messages[locale] ?? messages.en;

    return messages.en;
  }, [isLocaleSelectorEnabled, locale, messages]);

  const isLoadingMessages = isUserLoading ?? isLoading;

  const contextValue = useMemo<LocaleContextType>(() => ({ locale }), [locale]);

  useEffect(() => {
    if (!isLocaleSelectorEnabled) {
      setIsLoading(false);
      return;
    }

    const load = async () => {
      setIsLoading(true);

      if (messages[locale]) {
        setIsLoading(false);
        return;
      }

      try {
        const messageObject = await loadMessages(locale);
        setMessages((prev) => ({
          ...prev,
          [locale]: messageObject.default,
        }));
      } catch (error) {
        Sentry.captureException(error, { level: 'warning' });
      } finally {
        setIsLoading(false);
      }
    };

    load();
  }, [locale, isLocaleSelectorEnabled]);

  if (isLoadingMessages) return null;

  return (
    <LocaleContext.Provider value={contextValue}>
      <MessageProvider messages={localeMessages} locale={locale}>
        {children}
      </MessageProvider>
    </LocaleContext.Provider>
  );
};
