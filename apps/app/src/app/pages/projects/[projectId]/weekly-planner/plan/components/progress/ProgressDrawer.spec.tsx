import React from 'react';
import { activityFactory } from '@shape-construction/api/factories/activities';
import { booleanFlagFactory, featureFlagsFactory } from '@shape-construction/api/factories/feature-flags';
import {
  shiftActivityProgressLogFactory,
  shiftActivityProgressLogListFactory,
  shiftActivityProgressLogListItemFactory,
} from '@shape-construction/api/factories/progressLogs';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { planActivityFactory, weeklyWorkPlanFactory } from '@shape-construction/api/factories/weeklyWorkPlans';
import { getApiFeatureFlagsMockHandler } from '@shape-construction/api/handlers-factories/feature-flags';
import {
  patchApiProjectsProjectIdShiftActivitiesShiftActivityIdMockHandler,
  patchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects/shift-activities';
import {
  getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler,
  getApiProjectsProjectIdWeeklyWorkPlansPlanIdProgressLogsMockHandler,
} from '@shape-construction/api/handlers-factories/projects/weekly-work-plans';
import { createMemoryHistory } from 'history';
import { server, waitForRequest } from 'tests/mock-server';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import type { PlanActivityWithProgressLog } from '../../hooks/usePlanActivitiesWithProgressLogs';
import { ProgressDrawer } from './ProgressDrawer';

describe('<ProgressDrawer />', () => {
  describe('when defaultDate prop is present', () => {
    it('sets the initial date picker value', async () => {
      const weeklyWorkPlan = weeklyWorkPlanFactory();
      const planActivity = planActivityFactory();
      const project = projectFactory({ id: 'project-0' });
      server.use(getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan));
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}/progress-logs`],
      });
      const route = { path: '/projects/:projectId/weekly-planner/plans/:planId/progress-logs' };

      render(<ProgressDrawer open defaultDate="2024-04-01" planActivity={planActivity} onClose={jest.fn()} />, {
        history,
        route,
      });

      expect(await screen.findByRole('button', { name: '01-Apr-2024' })).toBeInTheDocument();
    });
  });

  describe('when streamlining-progress-logs is false', () => {
    describe('and planActivity is present', () => {
      it('renders the component with default values', async () => {
        const weeklyWorkPlan = weeklyWorkPlanFactory();
        const planActivity = planActivityFactory({
          shiftActivity: activityFactory({ description: 'Slab formwork', percentageCompleted: 40 }),
        });
        const project = projectFactory({ id: 'project-0' });
        server.use(getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan));
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}/progress-logs`],
        });
        const route = { path: '/projects/:projectId/weekly-planner/plans/:planId/progress-logs' };

        render(<ProgressDrawer open defaultDate="2024-04-01" planActivity={planActivity} onClose={jest.fn()} />, {
          history,
          route,
        });

        expect(await screen.findByText('Slab formwork')).toBeInTheDocument();
        expect(
          await screen.findByRole('button', {
            name: 'weeklyPlanner.workPlans.progressTracker.drawer.unlinkActivityCTA',
          })
        ).toBeInTheDocument();
        expect(await screen.findByRole('progressbar')).toHaveAttribute('aria-valuenow', '40');
      });

      describe('when weekly work plan has progress logs', () => {
        it('sets the default values', async () => {
          const weeklyWorkPlan = weeklyWorkPlanFactory();
          const planActivityWithProgressLog: PlanActivityWithProgressLog = {
            ...planActivityFactory({
              shiftActivity: activityFactory({ status: 'completed' }),
            }),
            progressLog: shiftActivityProgressLogListItemFactory({
              date: '2024-08-03',
              percentageCompleted: 100,
            }),
          };
          const progressLogs = shiftActivityProgressLogListFactory({
            entries: [
              shiftActivityProgressLogListItemFactory({
                id: 'progress-log-1',
                date: '2024-08-03',
                percentageCompleted: 100,
                shiftActivityId: planActivityWithProgressLog.shiftActivity.id,
              }),
              shiftActivityProgressLogListItemFactory({
                id: 'progress-log-2',
                date: '2024-08-02',
                percentageCompleted: 40,
                shiftActivityId: planActivityWithProgressLog.shiftActivity.id,
              }),
              shiftActivityProgressLogListItemFactory({
                id: 'progress-log-3',
                date: '2024-08-01',
                percentageCompleted: 60,
                shiftActivityId: planActivityWithProgressLog.shiftActivity.id,
              }),
            ],
          });
          const project = projectFactory({ id: 'project-0' });
          server.use(
            getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan),
            getApiProjectsProjectIdWeeklyWorkPlansPlanIdProgressLogsMockHandler(() => progressLogs)
          );
          const history = createMemoryHistory({
            initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}/progress-logs`],
          });
          const route = { path: '/projects/:projectId/weekly-planner/plans/:planId/progress-logs' };
          render(
            <ProgressDrawer
              open
              defaultDate="2024-08-01"
              planActivity={planActivityWithProgressLog}
              onClose={jest.fn()}
            />,
            {
              history,
              route,
            }
          );

          await waitFor(() => {
            expect(
              screen.getByRole('button', {
                name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
              })
            ).toBeEnabled();
          });
          await userEvent.click(
            screen.getByRole('button', {
              name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
            })
          );

          expect(
            await screen.findByLabelText(
              /weeklyPlanner.workPlans.progressTracker.drawer.shiftActivityForm.actualStart.label/
            )
          ).toHaveValue('2024-08-01T00:00');
          expect(
            screen.getByLabelText(/weeklyPlanner.workPlans.progressTracker.drawer.shiftActivityForm.actualFinish.label/)
          ).toHaveValue('2024-08-03T00:00');
        });
      });
    });
  });

  describe('when streamlining-progress-logs is true', () => {
    beforeEach(() => {
      const flagData = featureFlagsFactory({
        user: [booleanFlagFactory('streamlining-progress-logs', true)],
      });
      server.use(getApiFeatureFlagsMockHandler(() => flagData));
    });

    describe('and planActivity is present', () => {
      it('renders the component with default values', async () => {
        const weeklyWorkPlan = weeklyWorkPlanFactory();
        const planActivity = planActivityFactory({
          shiftActivity: activityFactory({ description: 'Slab formwork', percentageCompleted: 40 }),
        });
        const project = projectFactory({ id: 'project-0' });
        server.use(getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan));
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}/progress-logs`],
        });
        const route = { path: '/projects/:projectId/weekly-planner/plans/:planId/progress-logs' };

        render(<ProgressDrawer open defaultDate="2024-04-01" planActivity={planActivity} onClose={jest.fn()} />, {
          history,
          route,
        });

        expect(await screen.findByText('Slab formwork')).toBeInTheDocument();
        expect(
          await screen.findByRole('button', {
            name: 'weeklyPlanner.workPlans.progressTracker.drawer.unlinkActivityCTA',
          })
        ).toBeInTheDocument();
      });

      describe('when weekly work plan has progress logs', () => {
        it('sets the default values', async () => {
          const weeklyWorkPlan = weeklyWorkPlanFactory();
          const planActivityWithProgressLog: PlanActivityWithProgressLog = {
            ...planActivityFactory({
              shiftActivity: activityFactory({ status: 'completed' }),
            }),
            progressLog: shiftActivityProgressLogListItemFactory({
              date: '2024-08-03',
              percentageCompleted: 100,
              quantity: 14,
              units: 'kg',
            }),
          };
          const progressLogs = shiftActivityProgressLogListFactory({
            entries: [
              shiftActivityProgressLogListItemFactory({
                id: 'progress-log-1',
                date: '2024-08-03',
                percentageCompleted: 100,
                shiftActivityId: planActivityWithProgressLog.shiftActivity.id,
                quantity: 14,
                units: 'kg',
              }),
              shiftActivityProgressLogListItemFactory({
                id: 'progress-log-2',
                date: '2024-08-02',
                percentageCompleted: 40,
                quantity: 12,
                units: 'kg',
                shiftActivityId: planActivityWithProgressLog.shiftActivity.id,
              }),
              shiftActivityProgressLogListItemFactory({
                id: 'progress-log-3',
                date: '2024-08-01',
                percentageCompleted: 60,
                quantity: 10,
                units: 'kg',
                shiftActivityId: planActivityWithProgressLog.shiftActivity.id,
              }),
            ],
          });
          const project = projectFactory({ id: 'project-0' });
          server.use(
            getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan),
            getApiProjectsProjectIdWeeklyWorkPlansPlanIdProgressLogsMockHandler(() => progressLogs)
          );
          const history = createMemoryHistory({
            initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}/progress-logs`],
          });
          const route = { path: '/projects/:projectId/weekly-planner/plans/:planId/progress-logs' };
          render(
            <ProgressDrawer
              open
              defaultDate="2024-08-01"
              planActivity={planActivityWithProgressLog}
              onClose={jest.fn()}
            />,
            {
              history,
              route,
            }
          );

          await waitFor(() => {
            expect(
              screen.getByRole('button', {
                name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
              })
            ).toBeEnabled();
          });
          await userEvent.click(
            screen.getByRole('button', {
              name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
            })
          );

          expect(
            await screen.findByLabelText(
              /weeklyPlanner.workPlans.progressTracker.drawer.shiftActivityForm.actualStart.label/
            )
          ).toHaveValue('2024-08-01T00:00');
          expect(
            screen.getByLabelText(/weeklyPlanner.workPlans.progressTracker.drawer.shiftActivityForm.actualFinish.label/)
          ).toHaveValue('2024-08-03T00:00');

          expect(await screen.findByRole('progressbar')).toHaveAttribute('aria-valuenow', '100');
        });
      });
    });
  });

  describe('when no planActivity', () => {
    it('renders the component and elements', async () => {
      const weeklyWorkPlan = weeklyWorkPlanFactory();
      const project = projectFactory({ id: 'project-0' });
      server.use(getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan));
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}/progress-logs`],
      });
      const route = { path: '/projects/:projectId/weekly-planner/plans/:planId/progress-logs' };

      render(<ProgressDrawer open onClose={jest.fn()} />, {
        history,
        route,
      });

      expect(await screen.findByText('weeklyPlanner.workPlans.progressTracker.drawer.title')).toBeInTheDocument();
      expect(screen.getByText('weeklyPlanner.workPlans.progressTracker.drawer.dateLabel')).toBeInTheDocument();
      expect(
        screen.getByText('weeklyPlanner.workPlans.progressTracker.drawer.searchActivityLabel')
      ).toBeInTheDocument();
      expect(
        screen.getByPlaceholderText('weeklyPlanner.workPlans.progressTracker.drawer.searchActivityPlaceholder')
      ).toBeInTheDocument();
      expect(screen.getByText('weeklyPlanner.workPlans.progressTracker.drawer.progressLabel')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Decrease 5%' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Add 5%' })).toBeInTheDocument();
      expect(screen.getByText('weeklyPlanner.workPlans.progressTracker.drawer.descriptionLabel')).toBeInTheDocument();
      expect(
        screen.getByPlaceholderText('weeklyPlanner.workPlans.progressTracker.drawer.descriptionPlaceholder')
      ).toBeInTheDocument();
    });
  });

  describe('when user saves the form changes', () => {
    it('calls the onClose callback', async () => {
      const weeklyWorkPlan = weeklyWorkPlanFactory();
      const planActivityWithProgressLog: PlanActivityWithProgressLog = {
        ...planActivityFactory(),
        progressLog: shiftActivityProgressLogListItemFactory({
          id: 'progress-log-1',
          date: '2024-04-02',
          comment: 'This is a progress log description',
          percentageCompleted: 25,
        }),
      };
      const project = projectFactory({ id: 'project-0' });
      const onClose = jest.fn();
      server.use(
        getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan),
        patchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMockHandler(() =>
          shiftActivityProgressLogFactory()
        ),
        patchApiProjectsProjectIdShiftActivitiesShiftActivityIdMockHandler(() => activityFactory())
      );
      const updateProgressLogRequest = waitForRequest(
        'PATCH',
        '/api/projects/:projectId/shift_activities/:shiftActivityId/progress_logs/:progressLogId'
      );
      const updateShiftActivityRequest = waitForRequest(
        'PATCH',
        '/api/projects/:projectId/shift_activities/:shiftActivityId'
      );
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}/progress-logs`],
      });
      const route = { path: '/projects/:projectId/weekly-planner/plans/:planId/progress-logs' };
      render(<ProgressDrawer open planActivity={planActivityWithProgressLog} onClose={onClose} />, {
        history,
        route,
      });
      await userEvent.clear(
        await screen.findByPlaceholderText('weeklyPlanner.workPlans.progressTracker.drawer.descriptionPlaceholder')
      );
      await userEvent.type(
        await screen.findByPlaceholderText('weeklyPlanner.workPlans.progressTracker.drawer.descriptionPlaceholder'),
        '{selectall}This is the updated description'
      );
      await waitFor(() => {
        expect(
          screen.getByRole('button', {
            name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
          })
        ).toBeEnabled();
      });
      await userEvent.click(
        screen.getByRole('button', {
          name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
        })
      );

      await userEvent.click(
        await screen.findByRole('button', {
          name: 'weeklyPlanner.workPlans.progressTracker.drawer.doneCTA',
        })
      );

      await waitFor(() => {
        expect(updateProgressLogRequest).toBeDefined();
      });
      await waitFor(() => {
        expect(updateShiftActivityRequest).toBeDefined();
      });
      expect(onClose).toHaveBeenCalledTimes(1);
    });
  });

  describe('when user clicks on the next button', () => {
    it('renders the shift activity form fields', async () => {
      const weeklyWorkPlan = weeklyWorkPlanFactory();
      const planActivity = planActivityFactory({
        shiftActivity: activityFactory({
          description: 'Slab formwork',
          status: 'completed',
          actualStartDate: '2024-08-10T02:37',
          actualEndDate: '2024-10-15T01:32',
          percentageCompleted: 100,
        }),
      });
      const project = projectFactory({ id: 'project-0' });
      server.use(getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan));
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}/progress-logs`],
      });
      const route = { path: '/projects/:projectId/weekly-planner/plans/:planId/progress-logs' };
      render(<ProgressDrawer open defaultDate="2024-04-01" planActivity={planActivity} onClose={jest.fn()} />, {
        history,
        route,
      });

      await waitFor(() => {
        expect(
          screen.getByRole('button', {
            name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
          })
        ).toBeEnabled();
      });
      await userEvent.click(
        screen.getByRole('button', {
          name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
        })
      );

      expect(
        await screen.findByText(
          'weeklyPlanner.workPlans.progressTracker.drawer.shiftActivityForm.activityUpdates.title'
        )
      ).toBeInTheDocument();
      expect(screen.getByText('Slab formwork')).toBeInTheDocument();
      expect(screen.getByRole('option', { name: 'activities.form.fields.status.options.completed' })).toHaveValue(
        'completed'
      );
      expect(
        screen.getByLabelText(/weeklyPlanner.workPlans.progressTracker.drawer.shiftActivityForm.actualStart.label/)
      ).toHaveValue('2024-08-10T02:37');
      expect(
        screen.getByLabelText(/weeklyPlanner.workPlans.progressTracker.drawer.shiftActivityForm.actualFinish.label/)
      ).toHaveValue('2024-10-15T01:32');
      expect(
        screen.getByRole('button', {
          name: 'weeklyPlanner.workPlans.progressTracker.drawer.backCTA',
        })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', {
          name: 'weeklyPlanner.workPlans.progressTracker.drawer.doneCTA',
        })
      ).toBeInTheDocument();
    });

    it('updates the default shift activity status', async () => {
      const weeklyWorkPlan = weeklyWorkPlanFactory();
      const shiftActivity = activityFactory({
        description: 'Slab formwork',
        status: 'in_progress',
        actualStartDate: '2024-08-10T02:37',
        actualEndDate: '2024-10-15T01:32',
        percentageCompleted: 50,
      });
      const progressLog = shiftActivityProgressLogFactory({
        percentageCompleted: 100,
        shiftActivityId: shiftActivity.id,
      });
      const planActivityWithProgressLog: PlanActivityWithProgressLog = {
        ...planActivityFactory({ shiftActivity }),
        progressLog,
      };
      const progressLogs = shiftActivityProgressLogListFactory({ entries: [progressLog] });
      const project = projectFactory({ id: 'project-0' });
      server.use(
        getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan),
        getApiProjectsProjectIdWeeklyWorkPlansPlanIdProgressLogsMockHandler(() => progressLogs)
      );
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}/progress-logs`],
      });
      const route = { path: '/projects/:projectId/weekly-planner/plans/:planId/progress-logs' };
      render(<ProgressDrawer open planActivity={planActivityWithProgressLog} onClose={jest.fn()} />, {
        history,
        route,
      });

      await waitFor(() => {
        expect(
          screen.getByRole('button', {
            name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
          })
        ).toBeEnabled();
      });
      await userEvent.click(
        screen.getByRole('button', {
          name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
        })
      );

      expect(
        await screen.findByText(
          'weeklyPlanner.workPlans.progressTracker.drawer.shiftActivityForm.activityUpdates.title'
        )
      ).toBeInTheDocument();
      expect(
        await screen.findByRole('option', {
          name: 'activities.form.fields.status.options.completed',
          selected: true,
        })
      ).toBeInTheDocument();
    });
  });

  describe('when user clicks on the back button', () => {
    it('renders the progress log form fields', async () => {
      const weeklyWorkPlan = weeklyWorkPlanFactory();
      const planActivityWithProgressLog: PlanActivityWithProgressLog = {
        ...planActivityFactory({
          shiftActivity: activityFactory({ description: 'Slab formwork' }),
        }),
        progressLog: shiftActivityProgressLogListItemFactory({
          id: 'progress-log-1',
          date: '2024-08-15',
          comment: 'this is my comment',
          percentageCompleted: 50,
        }),
      };
      const project = projectFactory({ id: 'project-0' });
      server.use(getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan));
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}/progress-logs`],
      });
      const route = { path: '/projects/:projectId/weekly-planner/plans/:planId/progress-logs' };
      render(
        <ProgressDrawer open defaultDate="2024-04-01" planActivity={planActivityWithProgressLog} onClose={jest.fn()} />,
        { history, route }
      );
      await waitFor(() => {
        expect(
          screen.getByRole('button', {
            name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
          })
        ).toBeEnabled();
      });
      await userEvent.click(
        screen.getByRole('button', {
          name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
        })
      );

      await userEvent.click(
        await screen.findByRole('button', {
          name: 'weeklyPlanner.workPlans.progressTracker.drawer.backCTA',
        })
      );

      expect(await screen.findByRole('button', { name: '15-Aug-2024' })).toBeInTheDocument();
      expect(screen.getByText('Slab formwork')).toBeInTheDocument();
      expect(screen.getByRole('slider')).toHaveValue('50');
      expect(screen.getByLabelText('weeklyPlanner.workPlans.progressTracker.drawer.descriptionLabel')).toHaveValue(
        'this is my comment'
      );
      expect(
        screen.getByRole('button', {
          name: 'weeklyPlanner.workPlans.progressTracker.drawer.cancelCTA',
        })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', {
          name: 'weeklyPlanner.workPlans.progressTracker.drawer.nextCTA',
        })
      ).toBeInTheDocument();
    });
  });
});
