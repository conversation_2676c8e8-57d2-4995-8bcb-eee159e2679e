import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import { SHAPE, THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import Button from '@shape-construction/arch-ui/src/Button';
import { MagnifyingGlassIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { ArrowTopRightOnSquareIcon, PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Link from '@shape-construction/arch-ui/src/Link';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useMediaQuery } from '@shape-construction/hooks';

type PotentialChangesEmptyStateProps = {
  onBrowseChangeSignals: () => void;
  onCreateNewChange: () => void;
};

export const PotentialChangesEmptyState: React.FC<PotentialChangesEmptyStateProps> = ({
  onBrowseChangeSignals,
  onCreateNewChange,
}) => {
  const messages = useMessageGetter('controlCenter.commercialTracker.emptyStatePotentialChanges');
  const isMediumScreen = useMediaQuery(breakpoints.up('sm'));
  return (
    <div className="flex flex-col bg-gray-100 items-center justify-center grow sm:grow">
      <div className="max-w-[640px] p-4 flex flex-col items-center justify-center gap-6">
        <div className="flex flex-col items-center justify-center gap-3">
          <MagnifyingGlassIcon className="h-12 w-12 text-neutral-subtle" />
          <div className="flex flex-col items-center justify-center gap-2">
            <span className="text-lg leading-6 font-medium text-neutral-bold">{messages('title')}</span>
            <span className="text-sm leading-5 font-normal text-neutral-subtle text-center">
              {messages('subtitle')}
            </span>
          </div>
        </div>
        <div className="flex flex-col w-full sm:w-auto sm:flex-row gap-3">
          <Button
            onClick={onBrowseChangeSignals}
            size="sm"
            color="primary"
            fullWidth={!isMediumScreen}
            variant="outlined"
            leadingIcon={MagnifyingGlassIcon}
          >
            {messages('browseSignalsCTA')}
          </Button>
          <Button
            onClick={onCreateNewChange}
            size="sm"
            color="primary"
            fullWidth={!isMediumScreen}
            variant="contained"
            leadingIcon={PlusIcon}
          >
            {messages('newChangeCTA')}
          </Button>
        </div>
        <div className="flex flex-col w-full items-center gap-3">
          <h6 className="text-lg leading-6 font-medium text-neutral-bold">{messages('howItWorks')}:</h6>
          <ul className="flex flex-col sm:flex-row list-none p-0 m-0">
            <li className="flex flex-col gap-2 p-3 w-full sm:w-[220px] bg-neutral-white border border-neutral-subtlest rounded-tl-sm rounded-bl-sm">
              <div className="flex gap-1 items-center justify-center">
                <Badge label="1" shape={SHAPE.BASIC} theme={THEME.BLUE} />
                <span className="text-sm leading-5 font-medium text-neutral-bold">{messages('step1.title')}</span>
              </div>
              <p className="text-xs leading-5 font-normal text-center text-neutral-subtle">
                {messages('step1.description')}
              </p>
            </li>
            <li
              className={cn('flex flex-col gap-2 p-3 w-full sm:w-[220px] bg-neutral-white', {
                'border-y': isMediumScreen,
                'border-x': !isMediumScreen,
              })}
            >
              <div className="flex gap-1 items-center justify-center">
                <Badge label="2" shape={SHAPE.BASIC} theme={THEME.BLUE} />
                <span className="text-sm leading-5 font-medium text-neutral-bold">{messages('step2.title')}</span>
              </div>
              <p className="text-xs leading-5 font-normal text-center text-neutral-subtle">
                {messages('step2.description')}
              </p>
            </li>
            <li className="flex flex-col gap-2 p-3 w-full sm:w-[220px] bg-neutral-white border border-neutral-subtlest rounded-tr-sm rounded-br-sm">
              <div className="flex gap-1 items-center justify-center">
                <Badge label="3" shape={SHAPE.BASIC} theme={THEME.BLUE} />
                <span className="text-sm leading-5 font-medium text-neutral-bold">{messages('step3.title')}</span>
              </div>
              <p className="text-xs leading-5 font-normal text-center text-neutral-subtle">
                {messages('step3.description')}
              </p>
            </li>
          </ul>
        </div>
        <div>
          <Link
            href="https://help.shape.construction"
            color="primary"
            leadingIcon={ArrowTopRightOnSquareIcon}
            target="_blank"
          >
            {messages('learnMoreInTheHelpCenter')}
          </Link>
        </div>
      </div>
    </div>
  );
};
