import React from 'react';
import { shiftActivityProgressLogFactory } from '@shape-construction/api/factories/progressLogs';
import { dailyProgressProgressLogEntryFactory } from '@shape-construction/api/factories/shift_activities_overview';
import { planActivityFactory, weeklyWorkPlanFactory } from '@shape-construction/api/factories/weeklyWorkPlans';
import { getApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressMockHandler } from '@shape-construction/api/handlers-factories/projects/shift-activities';
import { getApiProjectsProjectIdWeeklyWorkPlansPlanIdProgressLogsMockHandler } from '@shape-construction/api/handlers-factories/projects/weekly-work-plans';
import { createMemoryHistory } from 'history';
import { server } from 'tests/mock-server';
import { render, screen, waitFor } from 'tests/test-utils';
import { ProgressDrawerGroup } from './ProgressDrawerGroup';

describe('<ProgressDrawerGroup />', () => {
  it('shows <ProgressLogDrawer/> if open is true', async () => {
    const history = createMemoryHistory({
      initialEntries: ['/projects/project-0/activities/activity-0'],
    });
    const route = { path: '/projects/:projectId/activities/:shiftActivityId' };
    const plan = weeklyWorkPlanFactory({
      startDate: '2024-06-10',
      endDate: '2024-06-13',
    });
    const planActivity = planActivityFactory({
      loggedDays: ['2024-06-11'],
      scheduledDays: ['2024-06-11', '2024-06-12'],
    });
    const weeklyWorkPlanProgressLog = shiftActivityProgressLogFactory({
      id: 'progress-log-1',
      date: '2024-06-11',
      shiftActivityId: planActivity.shiftActivity.id,
    });
    const progressLogEntry = dailyProgressProgressLogEntryFactory({
      date: '2024-06-11',
      progressLogId: weeklyWorkPlanProgressLog.id,
      weeklyWorkPlanId: plan.id,
    });
    server.use(
      getApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressMockHandler(() => ({
        entries: [progressLogEntry],
      })),
      getApiProjectsProjectIdWeeklyWorkPlansPlanIdProgressLogsMockHandler(() => ({
        entries: [weeklyWorkPlanProgressLog],
        meta: {
          firstEntryCursor: null,
          hasNextPage: false,
          hasPreviousPage: false,
          lastEntryCursor: null,
          total: 0,
        },
      }))
    );

    render(
      <ProgressDrawerGroup
        projectId="project-0"
        shiftActivityId="activity-0"
        row={progressLogEntry}
        closeModal={() => {}}
        open={true}
        progressOpen={false}
        openProgressModal={() => {}}
        closeProgressModal={() => {}}
      />,
      { history, route }
    );

    expect(
      await screen.findByRole('heading', {
        name: 'weeklyPlanner.workPlans.lookback.progressLogs.drawer.title',
      })
    ).toBeInTheDocument();
  });

  it('does not show <ProgressLogDrawer/> if open is false', async () => {
    const history = createMemoryHistory({
      initialEntries: ['/projects/project-0/activities/activity-0'],
    });
    const route = { path: '/projects/:projectId/activities/:shiftActivityId' };
    const plan = weeklyWorkPlanFactory({
      startDate: '2024-06-10',
      endDate: '2024-06-13',
    });
    const planActivity = planActivityFactory({
      loggedDays: ['2024-06-11'],
      scheduledDays: ['2024-06-11', '2024-06-12'],
    });
    const weeklyWorkPlanProgressLog = shiftActivityProgressLogFactory({
      id: 'progress-log-1',
      date: '2024-06-11',
      shiftActivityId: planActivity.shiftActivity.id,
    });
    const progressLogEntry = dailyProgressProgressLogEntryFactory({
      date: '2024-06-11',
      progressLogId: weeklyWorkPlanProgressLog.id,
      weeklyWorkPlanId: plan.id,
    });
    server.use(
      getApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressMockHandler(() => ({
        entries: [progressLogEntry],
      })),
      getApiProjectsProjectIdWeeklyWorkPlansPlanIdProgressLogsMockHandler(() => ({
        entries: [weeklyWorkPlanProgressLog],
        meta: {
          firstEntryCursor: null,
          hasNextPage: false,
          hasPreviousPage: false,
          lastEntryCursor: null,
          total: 0,
        },
      }))
    );

    render(
      <ProgressDrawerGroup
        projectId="project-0"
        shiftActivityId="activity-0"
        row={progressLogEntry}
        closeModal={() => {}}
        open={false}
        progressOpen={false}
        openProgressModal={() => {}}
        closeProgressModal={() => {}}
      />,
      { history, route }
    );

    const progressLogDrawerVisiblePromise = waitFor(() => {
      expect(
        screen.queryByRole('heading', {
          name: 'weeklyPlanner.workPlans.lookback.progressLogs.drawer.title',
        })
      ).toBeVisible();
    });
    await expect(progressLogDrawerVisiblePromise).rejects.toThrow();
  });

  it('shows <ProgressDrawer/> if progressOpen is true', async () => {
    const history = createMemoryHistory({
      initialEntries: ['/projects/project-0/activities/activity-0'],
    });
    const route = { path: '/projects/:projectId/activities/:shiftActivityId' };
    const plan = weeklyWorkPlanFactory({
      startDate: '2024-06-10',
      endDate: '2024-06-13',
    });
    const planActivity = planActivityFactory({
      loggedDays: ['2024-06-11'],
      scheduledDays: ['2024-06-11', '2024-06-12'],
    });
    const weeklyWorkPlanProgressLog = shiftActivityProgressLogFactory({
      id: 'progress-log-1',
      date: '2024-06-11',
      shiftActivityId: planActivity.shiftActivity.id,
    });
    const progressLogEntry = dailyProgressProgressLogEntryFactory({
      date: '2024-06-11',
      progressLogId: weeklyWorkPlanProgressLog.id,
      weeklyWorkPlanId: plan.id,
    });
    server.use(
      getApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressMockHandler(() => ({
        entries: [progressLogEntry],
      })),
      getApiProjectsProjectIdWeeklyWorkPlansPlanIdProgressLogsMockHandler(() => ({
        entries: [weeklyWorkPlanProgressLog],
        meta: {
          firstEntryCursor: null,
          hasNextPage: false,
          hasPreviousPage: false,
          lastEntryCursor: null,
          total: 0,
        },
      }))
    );

    render(
      <ProgressDrawerGroup
        projectId="project-0"
        shiftActivityId="activity-0"
        row={progressLogEntry}
        closeModal={() => {}}
        open={false}
        progressOpen={true}
        openProgressModal={() => {}}
        closeProgressModal={() => {}}
      />,
      { history, route }
    );

    expect(
      await screen.findByRole('heading', {
        name: 'weeklyPlanner.workPlans.progressTracker.drawer.title',
      })
    ).toBeInTheDocument();
  });

  it('does not show <ProgressDrawer/> if progressOpen is false', async () => {
    const history = createMemoryHistory({
      initialEntries: ['/projects/project-0/activities/activity-0'],
    });
    const route = { path: '/projects/:projectId/activities/:shiftActivityId' };
    const plan = weeklyWorkPlanFactory({
      startDate: '2024-06-10',
      endDate: '2024-06-13',
    });
    const planActivity = planActivityFactory({
      loggedDays: ['2024-06-11'],
      scheduledDays: ['2024-06-11', '2024-06-12'],
    });
    const weeklyWorkPlanProgressLog = shiftActivityProgressLogFactory({
      id: 'progress-log-1',
      date: '2024-06-11',
      shiftActivityId: planActivity.shiftActivity.id,
    });
    const progressLogEntry = dailyProgressProgressLogEntryFactory({
      date: '2024-06-11',
      progressLogId: weeklyWorkPlanProgressLog.id,
      weeklyWorkPlanId: plan.id,
    });
    server.use(
      getApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressMockHandler(() => ({
        entries: [progressLogEntry],
      })),
      getApiProjectsProjectIdWeeklyWorkPlansPlanIdProgressLogsMockHandler(() => ({
        entries: [weeklyWorkPlanProgressLog],
        meta: {
          firstEntryCursor: null,
          hasNextPage: false,
          hasPreviousPage: false,
          lastEntryCursor: null,
          total: 0,
        },
      }))
    );

    render(
      <ProgressDrawerGroup
        projectId="project-0"
        shiftActivityId="activity-0"
        row={progressLogEntry}
        closeModal={() => {}}
        open={false}
        progressOpen={false}
        openProgressModal={() => {}}
        closeProgressModal={() => {}}
      />,
      { history, route }
    );

    const progressLogDrawerVisiblePromise = waitFor(() => {
      expect(
        screen.queryByRole('heading', {
          name: 'weeklyPlanner.workPlans.progressTracker.drawer.updateTitle',
        })
      ).toBeVisible();
    });
    await expect(progressLogDrawerVisiblePromise).rejects.toThrow();
  });

  describe('when shift activity is undefined', () => {
    it('returns null and does not render a drawer', async () => {
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/activities/activity-0'],
      });
      const route = { path: '/projects/:projectId/activities/:shiftActivityId' };
      const progressLogEntry = dailyProgressProgressLogEntryFactory({
        date: '2024-06-11',
        progressLogId: 'progress-log-1',
        weeklyWorkPlanId: 'plan-1',
      });
      server.use(
        getApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressMockHandler(() => ({
          entries: [],
        })),
        getApiProjectsProjectIdWeeklyWorkPlansPlanIdProgressLogsMockHandler(() => ({
          entries: [],
          meta: {
            firstEntryCursor: null,
            hasNextPage: false,
            hasPreviousPage: false,
            lastEntryCursor: null,
            total: 0,
          },
        }))
      );

      const { container } = render(
        <ProgressDrawerGroup
          projectId="project-0"
          shiftActivityId="undefined-activity"
          row={progressLogEntry}
          closeModal={() => {}}
          open={true}
          progressOpen={true}
          openProgressModal={() => {}}
          closeProgressModal={() => {}}
        />,
        { history, route }
      );

      await waitFor(() => {
        expect(container.firstChild).toBeNull();
      });
      expect(
        screen.queryByRole('heading', {
          name: 'weeklyPlanner.workPlans.lookback.progressLogs.drawer.title',
        })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('heading', {
          name: 'weeklyPlanner.workPlans.progressTracker.drawer.title',
        })
      ).not.toBeInTheDocument();
    });
  });
});
