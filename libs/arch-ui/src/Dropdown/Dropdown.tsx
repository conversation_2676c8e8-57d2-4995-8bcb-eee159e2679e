import React, { forwardRef } from 'react';
import { DropdownMenu } from 'radix-ui';
import { cn } from '../utils/classes';
import './index.css';
import Divider from '../Divider';
import { ArrowLeftIcon, CheckIcon } from '../Icons/solid';

export const Root = DropdownMenu.Root;
export const Trigger = DropdownMenu.Trigger;
export const Sub = DropdownMenu.Sub;
export const RadioGroup = DropdownMenu.RadioGroup;
export const ItemIndicator = DropdownMenu.ItemIndicator;

export const Items = ({ children, ...props }: React.ComponentPropsWithoutRef<typeof DropdownMenu.Content>) => {
  return (
    <DropdownMenu.Portal>
      <>
        <div className="z-popover fixed inset-0 bg-black/50 z-overlay md:hidden" />
        <DropdownMenu.Content
          loop
          side="bottom"
          align="end"
          {...props}
          className={cn('z-popover min-w-52', props.className)}
        >
          <div
            className={cn(
              'overflow-y-auto bg-white rounded-t-lg rounded-b-none',
              'md:relative md:rounded-lg md:shadow-lg',
              'fixed bottom-0 w-screen md:w-auto md:bottom-auto',
              'animate-in slide-in-from-bottom z-popover md:animate-none'
            )}
          >
            {children}
          </div>
        </DropdownMenu.Content>
      </>
    </DropdownMenu.Portal>
  );
};

Items.displayName = 'DropdownItems';

type OwnProps = React.PropsWithChildren<{
  color?: 'default' | 'danger';
  icon?: React.FC<React.ComponentProps<'svg'>>;
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
}>;
type ExcludedMenuItemProps = 'color';

export const Item = forwardRef<
  React.ComponentRef<typeof DropdownMenu.Item>,
  Omit<React.ComponentPropsWithoutRef<typeof DropdownMenu.Item>, ExcludedMenuItemProps> & OwnProps
>(({ color = 'default', children, className, icon: Icon, startAdornment, endAdornment, ...props }, ref) => {
  return (
    <DropdownMenu.Item
      ref={ref}
      role="menuitem"
      className={cn(
        'relative flex cursor-default select-none items-center rounded-xs gap-3 py-3.5 md:py-2 px-4 text-sm outline-hidden transition-colors',
        'data-disabled:pointer-events-none data-disabled:bg-neutral-alpha-subltest data-disabled:opacity-50',
        'focus:bg-neutral-alpha-subltest-hovered',
        className
      )}
      {...props}
    >
      {startAdornment}
      {Icon && (
        <Icon
          aria-label="dropdown-item-icon"
          className={cn('h-4 w-4', {
            'text-neutral-subtle': color === 'default',
            'text-danger-subtle': color === 'danger',
          })}
        />
      )}
      <span
        className={cn('flex-1 text-left text-sm font-normal leading-5', {
          'text-neutral': color === 'default',
          'text-danger': color === 'danger',
        })}
      >
        {children}
      </span>
      {endAdornment}
    </DropdownMenu.Item>
  );
});

Item.displayName = 'Dropdown.Item';

type SubContentItemsProps = React.ComponentPropsWithoutRef<typeof DropdownMenu.SubContent> & {
  backLabel: React.ReactNode;
};

export const SubContentItems = ({ children, backLabel, ...props }: SubContentItemsProps) => {
  const contentRef = React.useRef<HTMLDivElement>(null);

  const handleBack = (e: any) => {
    e.preventDefault();
    // Simulate pressing ArrowLeft, which Radix uses to close a submenu
    contentRef.current?.dispatchEvent(new KeyboardEvent('keydown', { key: 'ArrowLeft', bubbles: true }));
  };

  return (
    <DropdownMenu.Portal>
      <>
        <div className="z-popover fixed inset-0 bg-black/50 z-overlay md:hidden" />
        <DropdownMenu.SubContent
          asChild
          loop
          {...props}
          className={cn('z-popover min-w-52', props.className)}
          sideOffset={4}
        >
          <div
            className={cn(
              'overflow-y-auto bg-white rounded-t-lg rounded-b-none',
              'md:relative md:rounded-lg md:shadow-lg',
              'fixed bottom-0 w-screen md:w-auto md:bottom-auto',
              'animate-in slide-in-from-bottom z-popover md:animate-none'
            )}
            ref={contentRef}
          >
            <span className="md:hidden">
              <Item onSelect={handleBack} startAdornment={<ArrowLeftIcon className="h-5 w-5" />}>
                {backLabel}
              </Item>
              <Divider orientation="horizontal" />
            </span>
            {children}
          </div>
        </DropdownMenu.SubContent>
      </>
    </DropdownMenu.Portal>
  );
};

SubContentItems.displayName = 'DropdownSubContentItems';

export const SubTrigger = forwardRef<
  React.ComponentRef<typeof DropdownMenu.SubTrigger>,
  Omit<React.ComponentPropsWithoutRef<typeof DropdownMenu.SubTrigger>, ExcludedMenuItemProps> & OwnProps
>(({ color = 'default', children, className, icon: Icon, startAdornment, endAdornment, ...props }, ref) => {
  return (
    <DropdownMenu.SubTrigger
      ref={ref}
      role="menuitem"
      className={cn(
        'relative flex cursor-default select-none items-center rounded-xs gap-3 py-3.5 md:py-2 px-4 text-sm outline-hidden transition-colors',
        'data-disabled:pointer-events-none data-disabled:bg-neutral-alpha-subltest data-disabled:opacity-50',
        'focus:bg-neutral-alpha-subltest-hovered',
        className
      )}
      {...props}
    >
      {startAdornment}
      {Icon && (
        <Icon
          aria-label="dropdown-item-icon"
          className={cn('h-4 w-4 shrink-0', {
            'text-neutral-subtle': color === 'default',
            'text-danger-subtle': color === 'danger',
          })}
        />
      )}
      <span
        className={cn('flex-1 text-left text-sm font-normal leading-5', {
          'text-neutral': color === 'default',
          'text-danger': color === 'danger',
        })}
      >
        {children}
      </span>
      {endAdornment}
    </DropdownMenu.SubTrigger>
  );
});

SubTrigger.displayName = 'DropdownSubTrigger';

export const RadioItem = forwardRef<
  React.ComponentRef<typeof DropdownMenu.RadioItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenu.RadioItem>
>(({ children, className, ...props }, ref) => {
  return (
    <DropdownMenu.RadioItem
      ref={ref}
      role="menuitem"
      className={cn(
        'relative flex cursor-pointer select-none items-center rounded-xs gap-3 py-3.5 md:py-2 px-4 text-sm outline-hidden transition-colors',
        'data-disabled:pointer-events-none data-disabled:bg-neutral-alpha-subltest data-disabled:opacity-50',
        'focus:bg-neutral-alpha-subltest-hovered',
        'data-[state=checked]:bg-selected-subtle data-[state=checked]:text-neutral',
        className
      )}
      {...props}
    >
      <span className={cn('flex-1 text-left text-sm font-normal leading-5 text-neutral')}>{children}</span>
      <ItemIndicator>
        <CheckIcon className="h-5 w-5 text-indigo-500" />
      </ItemIndicator>
    </DropdownMenu.RadioItem>
  );
});
RadioItem.displayName = 'DropdownRadioItem';
