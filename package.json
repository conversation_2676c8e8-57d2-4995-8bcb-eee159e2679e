{"name": "shape-frontend", "description": "Monorepo for all shape frontend apps and libs", "packageManager": "pnpm@9.0.4", "engines": {"node": "v22.18.0", "pnpm": "9.0.4"}, "scripts": {"compile": "turbo run compile", "lint": "turbo run lint", "lint:autofix": "turbo run lint:autofix", "test:ci": "turbo run test:ci", "build": "turbo run build", "build:storybook": "turbo run build:storybook", "commitlint:pr": "commitlint --from=master", "dependencies:sync": "manypkg fix"}, "dependencies": {"@biomejs/biome": "2.0.6", "@commitlint/cli": "19.8.1", "@manypkg/cli": "0.24.0", "husky": "9.1.7", "turbo": "2.5.4", "typescript": "5.8.3", "vercel": "44.2.0"}, "pnpm": {"overrides": {"jsonpath-plus@<=10.3.0": "10.3.0"}}}