describe('Navigation functionality', () => {
    it('should populate next change record when next button is clicked', () => {
        // Test case 1
    });

    describe("when next button is clicked", () => {
        describe("when there is next change", () => {
            it('populates next change record', () => {
            });
        });

        describe("when there is no next change", () => {
            describe("when there are no next pages", () => {
                it('disables next button', () => {
                });
            });
        });
    })

    it('should populate previous change record when previous button is clicked', () => {
        // Test case 2  
    });

    it('should fetch next page and show next change when at last entry with more pages', () => {
        // Test case 3 - most complex test
    });

    it('should disable next button when no more changes available', () => {
        // Test case 4
    });

    it('should disable previous button when at first change', () => {
        // Test case 5
    });
});