{"name": "@shape-construction/feature-flags", "private": true, "version": "1.0.0", "description": "Library for accessing feature flags", "main": "./src/index.ts", "types": "./src/index.ts", "engines": {"node": "v22.18.0", "pnpm": "9.0.4"}, "scripts": {"compile": "tsc -p tsconfig.json", "lint": "biome check .", "lint:autofix": "biome check --write .", "test": "jest --watch", "test:ci": "jest --ci --maxWorkers=100% --coverage"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@testing-library/react": "16.2.0", "msw": "2.7.0", "typescript": "5.8.3"}, "peerDependencies": {"@tanstack/react-query": "5.52.1", "react": "18.3.1"}, "dependencies": {"@shape-construction/api": "workspace:*", "@shape-construction/utils": "workspace:*", "jest-fixed-jsdom": "0.0.9", "uuid": "^9.0.0"}}