import { render, screen } from 'tests/test-utils';
import { PotentialChangesEmptyState } from './PotentialChangesEmptyState';

describe('<PotentialChangesEmptyState />', () => {
  describe("when 'Browse signals' button is clicked", () => {
    it('calls onBrowseChangeSignals', async () => {
      const onBrowseChangeSignals = jest.fn();
      const { user } = render(
        <PotentialChangesEmptyState onBrowseChangeSignals={onBrowseChangeSignals} onCreateNewChange={jest.fn()} />
      );

      await user.click(screen.getByText('controlCenter.commercialTracker.emptyStatePotentialChanges.browseSignalsCTA'));

      expect(onBrowseChangeSignals).toHaveBeenCalled();
    });
  });

  describe("when 'Create new change' button is clicked", () => {
    it('calls onCreateNewChange', async () => {
      const onCreateNewChange = jest.fn();
      const { user } = render(
        <PotentialChangesEmptyState onBrowseChangeSignals={jest.fn()} onCreateNewChange={onCreateNewChange} />
      );

      await user.click(screen.getByText('controlCenter.commercialTracker.emptyStatePotentialChanges.newChangeCTA'));

      expect(onCreateNewChange).toHaveBeenCalled();
    });
  });
});
