import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { MessageDeleted as DefaultMessageDeleted, useChannelStateContext, useMessageContext } from 'stream-chat-react';
import { GROUP_CHANNEL_TYPES } from '../Channel/constants/channelTypes';

export const MessageDeleted = () => {
  const { channel } = useChannelStateContext();
  const { isMyMessage, message } = useMessageContext();

  const channelMessages = channel.state.messages;
  const isGroupChannel = GROUP_CHANNEL_TYPES.includes(channel.type);
  const channelMessagesLength = channelMessages?.length || 0;
  const index = channelMessages.findIndex((msg) => msg.id === message.id);
  const nextMessage = index !== channelMessagesLength - 1 ? channelMessages[index + 1] : undefined;
  const lastMessageInGroup = index === channelMessagesLength - 1 || nextMessage?.user?.id !== message.user?.id;

  return (
    <div className="flex flex-col">
      <div
        className={cn('gap-1 relative mb-1', {
          'flex-row-reverse self-end': isMyMessage(),
          'flex-row pl-11 self-start': !isMyMessage(),
          'mb-4': lastMessageInGroup,
          'pl-11': !isMyMessage() && !lastMessageInGroup && isGroupChannel,
        })}
      >
        <DefaultMessageDeleted message={message} />
      </div>
    </div>
  );
};
