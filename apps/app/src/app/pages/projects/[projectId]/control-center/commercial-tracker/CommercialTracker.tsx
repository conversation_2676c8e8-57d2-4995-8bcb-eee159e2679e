import React from 'react';
import * as Sentry from '@sentry/react';
import type {
  PotentialChangeDetailsBasicSchema,
  PotentialChangeSchema,
  ProjectSchema,
} from '@shape-construction/api/src/types';
import { useModal } from '@shape-construction/hooks';
import { useSuspenseInfiniteQuery } from '@tanstack/react-query';
import { getPotentialChangesInfiniteQueryOptions } from 'app/queries/control-center/commercial-tracker';
import { ChangeSignalsSummary } from './components/ChangeSignalsSummary';
import { PotentialChangeSignalsDrawer } from './components/PotentialChangeSignalsDrawer';
import { PotentialChangesEmptyState } from './components/PotentialChangesEmptyState';
import { PotentialChangesEmptyStateSelectionMode } from './components/PotentialChangesEmptyStateSelectionMode';
import { PotentialChangesTable } from './components/PotentialChangesTable';

type CommercialTrackerProps = {
  project: ProjectSchema;
  onOpenDrawer: () => void;
  isSelectionMode?: boolean;
  selectedPotentialChangeIds: Array<PotentialChangeSchema['id']> | undefined;
  setSelectedPotentialChangeIds: React.Dispatch<React.SetStateAction<Array<PotentialChangeSchema['id']> | undefined>>;
  titleRef: React.RefObject<HTMLInputElement>;
  onCreatePotentialChange: () => void;
  newRecordId?: PotentialChangeSchema['id'];
};

export const CommercialTracker: React.FC<CommercialTrackerProps> = ({
  project,
  onOpenDrawer,
  isSelectionMode,
  selectedPotentialChangeIds,
  setSelectedPotentialChangeIds,
  titleRef,
  onCreatePotentialChange,
  newRecordId,
}) => {
  const [selectedPotentialChangeForSignals, setSelectedPotentialChangeForSignals] =
    React.useState<PotentialChangeDetailsBasicSchema>();

  const {
    data: potentialChangesData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useSuspenseInfiniteQuery(getPotentialChangesInfiniteQueryOptions(project.id));

  const potentialChanges = potentialChangesData.entries;
  const totalChanges = potentialChangesData.meta.total;

  const {
    open: isPotentialChangeSignalsDrawerOpen,
    openModal: openPotentialChangeSignalsDrawer,
    closeModal: closePotentialChangeSignalsDrawer,
  } = useModal(false);

  const currentIndex = selectedPotentialChangeForSignals
    ? potentialChanges.findIndex((change) => change.id === selectedPotentialChangeForSignals.id)
    : -1;

  const isPreviousDisabled = currentIndex <= 0;
  const isNextDisabled = currentIndex === potentialChanges.length - 1 && !hasNextPage;

  const onViewChangeSignals = (potentialChange: PotentialChangeDetailsBasicSchema) => {
    setSelectedPotentialChangeForSignals(potentialChange);
    openPotentialChangeSignalsDrawer();
  };

  const onPotentialChangeSignalsClose = () => {
    setSelectedPotentialChangeForSignals(undefined);
    closePotentialChangeSignalsDrawer();
  };

  const onPreviousChange = () => {
    if (currentIndex > 0) {
      const previousChange = potentialChanges[currentIndex - 1];
      setSelectedPotentialChangeForSignals(previousChange);
    }
  };

  const onNextChange = async () => {
    if (currentIndex < potentialChanges.length - 1) {
      setSelectedPotentialChangeForSignals(potentialChanges[currentIndex + 1]);
    } else if (hasNextPage && !isFetchingNextPage) {
      try {
        const result = await fetchNextPage();
        if (result.data) {
          const freshEntries = result.data.entries;
          const nextChange = freshEntries[currentIndex + 1];
          if (nextChange) {
            setSelectedPotentialChangeForSignals(nextChange);
          }
        }
      } catch (error) {
        Sentry.captureException(error);
      }
    }
  };

  const renderEmptyState = () => {
    if (isSelectionMode) {
      return <PotentialChangesEmptyStateSelectionMode />;
    }

    return (
      <PotentialChangesEmptyState onBrowseChangeSignals={onOpenDrawer} onCreateNewChange={onCreatePotentialChange} />
    );
  };

  const renderPotentialChangesContent = () => {
    if (potentialChanges.length === 0) {
      return renderEmptyState();
    }

    return (
      <>
        <PotentialChangesTable
          potentialChanges={potentialChanges}
          isSelectionMode={isSelectionMode}
          selectedPotentialChangeIds={selectedPotentialChangeIds}
          setSelectedPotentialChangeIds={setSelectedPotentialChangeIds}
          onViewChangeSignals={onViewChangeSignals}
          titleRef={titleRef}
          hasNextPage={hasNextPage}
          fetchNextPage={fetchNextPage}
          isFetchingNextPage={isFetchingNextPage}
          newRecordId={newRecordId}
          selectedPotentialChangeForSignals={selectedPotentialChangeForSignals}
        />
        {selectedPotentialChangeForSignals && (
          <PotentialChangeSignalsDrawer
            selectedPotentialChange={selectedPotentialChangeForSignals}
            setSelectedPotentialChange={setSelectedPotentialChangeForSignals}
            isOpen={isPotentialChangeSignalsDrawerOpen}
            onClose={onPotentialChangeSignalsClose}
            onPreviousChange={onPreviousChange}
            onNextChange={onNextChange}
            isPreviousDisabled={isPreviousDisabled}
            isNextDisabled={isNextDisabled}
          />
        )}
      </>
    );
  };

  return (
    <div className="flex flex-col grow">
      <ChangeSignalsSummary
        isSelectionMode={isSelectionMode}
        totalChanges={totalChanges}
        onNewChangeClick={onCreatePotentialChange}
      />

      {renderPotentialChangesContent()}
    </div>
  );
};
