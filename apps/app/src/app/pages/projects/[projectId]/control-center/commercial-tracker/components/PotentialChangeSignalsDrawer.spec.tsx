import { changeSignalsIssuesFactory, potentialChangeFactory } from '@shape-construction/api/factories/control-center';
import {
  getApiProjectsPotentialChangeDetailsMockHandler,
  getApiProjectsProjectIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects';
import {
  getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler,
  getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler,
  postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMockHandler,
  postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMockHandler,
} from '@shape-construction/api/handlers-factories/projects/control-center';
import { server } from 'tests/mock-server';
import { findAsyncToaster, render, screen, userEvent } from 'tests/test-utils';
import { PotentialChangeSignalsDrawer } from './PotentialChangeSignalsDrawer';

describe('<PotentialChangeSignalsDrawer />', () => {
  describe('when the change signal is successfully linked', () => {
    it('show success toast', async () => {
      server.use(
        getApiProjectsProjectIdMockHandler(),
        getApiProjectsPotentialChangeDetailsMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler(),
        postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMockHandler()
      );
      render(
        <PotentialChangeSignalsDrawer
          isOpen
          onClose={jest.fn()}
          selectedPotentialChange={potentialChangeFactory()}
          setSelectedPotentialChange={jest.fn()}
          onPreviousChange={jest.fn()}
          onNextChange={jest.fn()}
          isPreviousDisabled
          isNextDisabled
        />,
        { renderToast: true }
      );

      const startLinkingButton = await screen.findByRole('button', {
        name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.linkSignals',
      });
      await userEvent.click(startLinkingButton);
      const checkboxes = screen.getAllByRole('checkbox');
      await userEvent.click(checkboxes[0]);
      const linkButton = screen.getByRole('button', { name: 'controlCenter.changeSignals.actions.link' });
      await userEvent.click(linkButton);

      expect(await findAsyncToaster('controlCenter.changeSignals.linkStatus.linking.success')).toBeInTheDocument();
    });
  });

  describe('when the change signal linking fails', () => {
    it('show error toast', async () => {
      server.use(
        getApiProjectsProjectIdMockHandler(),
        getApiProjectsPotentialChangeDetailsMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler(),
        postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMockHandler(
          undefined,
          {
            status: 500,
          }
        )
      );
      render(
        <PotentialChangeSignalsDrawer
          isOpen
          onClose={jest.fn()}
          selectedPotentialChange={potentialChangeFactory()}
          setSelectedPotentialChange={jest.fn()}
          onPreviousChange={jest.fn()}
          onNextChange={jest.fn()}
          isPreviousDisabled
          isNextDisabled
        />,
        { renderToast: true }
      );

      const startLinkingButton = await screen.findByRole('button', {
        name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.linkSignals',
      });
      await userEvent.click(startLinkingButton);
      const checkboxes = screen.getAllByRole('checkbox');
      await userEvent.click(checkboxes[0]);
      const linkButton = screen.getByRole('button', { name: 'controlCenter.changeSignals.actions.link' });
      await userEvent.click(linkButton);

      expect(await findAsyncToaster('controlCenter.changeSignals.linkStatus.linking.failed')).toBeInTheDocument();
    });
  });

  describe('when the change signal is unlinked', () => {
    describe('when the unlinking succeeds', () => {
      it('shows a successful unlinking toast', async () => {
        const changeSignal = changeSignalsIssuesFactory();
        const potentialChange = potentialChangeFactory({ signals: [changeSignal], signalsCount: 1 });
        const setSelectedPotentialChange = jest.fn();
        const onClose = jest.fn();
        server.use(
          getApiProjectsProjectIdMockHandler(),
          getApiProjectsPotentialChangeDetailsMockHandler(() => potentialChange),
          getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
          getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler(),
          postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMockHandler()
        );
        render(
          <PotentialChangeSignalsDrawer
            isOpen
            onClose={onClose}
            selectedPotentialChange={potentialChange}
            setSelectedPotentialChange={setSelectedPotentialChange}
            onPreviousChange={jest.fn()}
            onNextChange={jest.fn()}
            isPreviousDisabled
            isNextDisabled
          />,
          { renderToast: true }
        );

        await userEvent.click(
          await screen.findByLabelText('controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.unlink')
        );
        await userEvent.click(
          await screen.findByRole('button', { name: 'controlCenter.commercialTracker.modals.unlink.unlinkCTA' })
        );

        expect(await findAsyncToaster('controlCenter.changeSignals.linkStatus.unlinking.success')).toBeInTheDocument();
      });
    });
    describe('when the unlinking fails', () => {
      it('shows an error toast', async () => {
        const changeSignal = changeSignalsIssuesFactory();
        const potentialChange = potentialChangeFactory({ signals: [changeSignal], signalsCount: 1 });
        const setSelectedPotentialChange = jest.fn();
        const onClose = jest.fn();
        server.use(
          getApiProjectsProjectIdMockHandler(),
          getApiProjectsPotentialChangeDetailsMockHandler(() => potentialChange),
          getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
          getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler(),
          postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMockHandler(
            undefined,
            { status: 500 }
          )
        );
        render(
          <PotentialChangeSignalsDrawer
            isOpen
            onClose={onClose}
            selectedPotentialChange={potentialChange}
            setSelectedPotentialChange={setSelectedPotentialChange}
            onPreviousChange={jest.fn()}
            onNextChange={jest.fn()}
            isPreviousDisabled
            isNextDisabled
          />,
          { renderToast: true }
        );

        await userEvent.click(
          await screen.findByLabelText('controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.unlink')
        );
        await userEvent.click(
          await screen.findByRole('button', { name: 'controlCenter.commercialTracker.modals.unlink.unlinkCTA' })
        );

        expect(await findAsyncToaster('controlCenter.changeSignals.linkStatus.unlinking.failed')).toBeInTheDocument();
      });
    });
  });

  describe('when previous button is clicked', () => {
    it('calls onPreviousChange', async () => {
      server.use(
        getApiProjectsProjectIdMockHandler(),
        getApiProjectsPotentialChangeDetailsMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler()
      );
      const onPreviousChange = jest.fn();
      const { user } = render(
        <PotentialChangeSignalsDrawer
          isOpen
          onClose={jest.fn()}
          selectedPotentialChange={potentialChangeFactory()}
          setSelectedPotentialChange={jest.fn()}
          onPreviousChange={onPreviousChange}
          onNextChange={jest.fn()}
          isPreviousDisabled={false}
          isNextDisabled={false}
        />
      );

      await user.click(
        await screen.findByLabelText('controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.previous')
      );

      expect(onPreviousChange).toHaveBeenCalled();
    });
  });

  describe('when next button is clicked', () => {
    it('calls onNextChange', async () => {
      server.use(
        getApiProjectsProjectIdMockHandler(),
        getApiProjectsPotentialChangeDetailsMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler()
      );
      const onNextChange = jest.fn();
      const { user } = render(
        <PotentialChangeSignalsDrawer
          isOpen
          onClose={jest.fn()}
          selectedPotentialChange={potentialChangeFactory()}
          setSelectedPotentialChange={jest.fn()}
          onPreviousChange={jest.fn()}
          onNextChange={onNextChange}
          isPreviousDisabled={false}
          isNextDisabled={false}
        />
      );

      await user.click(
        await screen.findByLabelText('controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.next')
      );

      expect(onNextChange).toHaveBeenCalled();
    });
  });

  describe('when isPreviousDisabled is true', () => {
    it('disables previous button', async () => {
      server.use(
        getApiProjectsProjectIdMockHandler(),
        getApiProjectsPotentialChangeDetailsMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler()
      );
      render(
        <PotentialChangeSignalsDrawer
          isOpen
          onClose={jest.fn()}
          selectedPotentialChange={potentialChangeFactory()}
          setSelectedPotentialChange={jest.fn()}
          onPreviousChange={jest.fn()}
          onNextChange={jest.fn()}
          isPreviousDisabled={true}
          isNextDisabled={false}
        />
      );

      expect(
        screen.findByLabelText('controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.previous')
      ).toBeDisabled();
    });
  });

  describe('when isNextDisabled is true', () => {
    it('disables next button', async () => {
      server.use(
        getApiProjectsProjectIdMockHandler(),
        getApiProjectsPotentialChangeDetailsMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler()
      );
      render(
        <PotentialChangeSignalsDrawer
          isOpen
          onClose={jest.fn()}
          selectedPotentialChange={potentialChangeFactory()}
          setSelectedPotentialChange={jest.fn()}
          onPreviousChange={jest.fn()}
          onNextChange={jest.fn()}
          isPreviousDisabled={false}
          isNextDisabled={true}
        />
      );

      expect(
        await screen.findByLabelText('controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.next')
      ).toBeDisabled();
    });
  });
});
