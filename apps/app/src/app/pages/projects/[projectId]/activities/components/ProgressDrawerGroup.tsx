import { useMemo } from 'react';
import type {
  ShiftActivityOverviewDailyProgressEntryProgressLogPartialSchema,
  ShiftActivityOverviewDailyProgressEntrySchema,
  WeeklyWorkPlanActivitySchema,
  WeeklyWorkPlanSchema,
} from '@shape-construction/api/src/types';
import { useShiftActivity } from 'app/queries/activities/activities';
import { useShiftActivityProgressLog } from 'app/queries/progressLogs/progressLogs';
import { useWeeklyWorkPlan, useWeeklyWorkPlanActivities } from 'app/queries/weeklyWorkPlans/weeklyWorkPlans';
import { ProgressLogDrawer } from '../../weekly-planner/[planId]/lookback/components/ProgressLogDrawer/ProgressLogDrawer';
import { ProgressDrawer } from '../../weekly-planner/plan/components/progress/ProgressDrawer';
import { useFindShiftActivityProgressLog } from '../../weekly-planner/plan/hooks/useFindShiftActivityProgressLog';
import type { PlanActivityWithProgressLog } from '../../weekly-planner/plan/hooks/usePlanActivitiesWithProgressLogs';

export type DailyProgressRow = ShiftActivityOverviewDailyProgressEntrySchema &
  ShiftActivityOverviewDailyProgressEntryProgressLogPartialSchema;

type ProgressDrawerGroupProps = {
  projectId: string;
  shiftActivityId: string;
  row?: DailyProgressRow;
  closeModal: () => void;
  open: boolean;
  progressOpen: boolean;
  openProgressModal: () => void;
  closeProgressModal: () => void;
};

export const ProgressDrawerGroup = ({
  projectId,
  shiftActivityId,
  row,
  closeModal,
  open,
  progressOpen,
  openProgressModal,
  closeProgressModal,
}: ProgressDrawerGroupProps) => {
  const { data: workPlanActivities } = useWeeklyWorkPlanActivities(projectId, row?.weeklyWorkPlanId!);
  const { data: shiftActivity } = useShiftActivity(projectId, shiftActivityId);
  const { data: progressLog } = useShiftActivityProgressLog(projectId, shiftActivityId, row?.progressLogId!);
  const { data: plan } = useWeeklyWorkPlan(projectId, row?.weeklyWorkPlanId!);

  const selectedActivity = useMemo(() => {
    return workPlanActivities?.entries?.find(
      (workPlanActivity) => workPlanActivity.shiftActivity.id === shiftActivityId
    );
  }, [workPlanActivities, shiftActivityId, open]);

  const planProgressLog = useFindShiftActivityProgressLog({
    enabled: row !== undefined,
    progressLogDate: row?.date,
    shiftActivityId: selectedActivity?.shiftActivity?.id,
    planId: row?.weeklyWorkPlanId!,
  });

  const shiftActivityProgressLog = progressLog ?? planProgressLog;
  const planActivity = selectedActivity ?? { shiftActivity };
  const planActivityWithProgressLog = { ...(planActivity ?? {}), progressLog: shiftActivityProgressLog };

  if (!planActivity || !planActivity.shiftActivity) return null;

  return (
    <>
      <ProgressDrawer
        open={progressOpen}
        planActivity={planActivityWithProgressLog as PlanActivityWithProgressLog}
        defaultDate={row?.date}
        onClose={closeProgressModal}
        isFromLookbackTable
      />
      <ProgressLogDrawer
        open={open}
        onClose={closeModal}
        planActivity={planActivity as WeeklyWorkPlanActivitySchema}
        progressLog={progressLog}
        plan={(plan ?? {}) as WeeklyWorkPlanSchema}
        selectedDate={row?.date!}
        onEditProgress={() => {
          openProgressModal();
          closeModal();
        }}
      />
    </>
  );
};
