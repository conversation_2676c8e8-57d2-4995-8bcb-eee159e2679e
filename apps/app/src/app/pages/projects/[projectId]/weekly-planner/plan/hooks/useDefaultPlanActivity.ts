import type { WeeklyWorkPlanSchema } from '@shape-construction/api/src/types';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useWeeklyWorkPlanProgressLogs } from 'app/queries/weeklyWorkPlans/weeklyWorkPlans';
import { useParams } from 'react-router';
import { baseDefaultValues, type FormValues } from '../components/progress/form/useProgressLogForm';
import type { PlanActivityWithProgressLog } from './usePlanActivitiesWithProgressLogs';

type Params = {
  planId: WeeklyWorkPlanSchema['id'];
};

export const useDefaultPlanActivity = (
  planActivity?: PlanActivityWithProgressLog
): FormValues['weeklyWorkPlanActivity'] => {
  const { planId } = useParams<Params>() as Params;
  const project = useCurrentProject();
  const { data: progressLogsData } = useWeeklyWorkPlanProgressLogs(project.id, planId, undefined, {
    query: { enabled: Boolean(planId && planActivity) },
  });

  const shiftActivity = planActivity?.shiftActivity;
  if (!shiftActivity) {
    return baseDefaultValues.weeklyWorkPlanActivity;
  }

  const activityProgressLogs =
    progressLogsData?.entries?.filter((log) => log.shiftActivityId === shiftActivity.id) ?? [];

  const lastLogDate = activityProgressLogs.at(-1)?.date ?? null;
  const firstLogDate = activityProgressLogs[0]?.date ?? null;
  const actualStartDate = shiftActivity.actualStartDate ?? lastLogDate;
  const actualEndDate = shiftActivity.actualEndDate ?? firstLogDate;

  return {
    ...planActivity,
    shiftActivity: {
      ...shiftActivity,
      actualStartDate,
      actualEndDate,
    },
  };
};
