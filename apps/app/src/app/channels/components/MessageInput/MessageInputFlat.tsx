import React, { useCallback, useMemo, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import * as FileUpload from '@shape-construction/arch-ui/src/FileUpload';
import { PaperAirplaneIcon, PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { MAX_FILE_SIZE_IN_BYTES } from 'app/constants/FileUpload';
import { useFileUploadValidator } from 'app/hooks/useFileUploadValidator';
import { useDropzone } from 'react-dropzone';
import type { MessageComposerState } from 'stream-chat';
import {
  AttachmentPreviewList as DefaultAttachmentPreviewList,
  AudioRecorder as DefaultAudioRecorder,
  CooldownTimer as DefaultCooldownTimer,
  LinkPreviewList as DefaultLinkPreviewList,
  RecordingPermissionDeniedNotification as DefaultRecordingPermissionDeniedNotification,
  StartRecordingAudioButton as DefaultStartRecordingAudioButton,
  TextareaComposer as DefaultTextareaComposer,
  QuotedMessagePreviewHeader,
  RecordingPermission,
  useAttachmentManagerState,
  useComponentContext,
  useMessageComposer,
  useMessageComposerHasSendableData,
  useMessageInputContext,
  useStateStore,
  useTranslationContext,
} from 'stream-chat-react';
import DefaultQuotedMessagePreview from '../Message/QuotedMessage';

export enum RecordingAttachmentType {
  VOICE_RECORDING = 'voiceRecording',
}

const messageComposerStateStoreSelector = (state: MessageComposerState) => ({
  quotedMessage: state.quotedMessage,
});

export const MessageInputFlat = () => {
  const { t } = useTranslationContext('MessageInputFlat');
  const messages = useMessageGetter('channels.channel.messageInput');
  const errors = useMessageGetter('errors.fileUpload');
  const {
    asyncMessagesMultiSendEnabled,
    cooldownRemaining,
    handleSubmit,
    hideSendButton,
    recordingController,
    setCooldownRemaining,
  } = useMessageInputContext('MessageInputFlat');
  const hasSendableData = useMessageComposerHasSendableData();

  const {
    AttachmentPreviewList = DefaultAttachmentPreviewList,
    AudioRecorder = DefaultAudioRecorder,
    CooldownTimer = DefaultCooldownTimer,
    EmojiPicker,
    QuotedMessagePreview = DefaultQuotedMessagePreview,
    LinkPreviewList = DefaultLinkPreviewList,
    RecordingPermissionDeniedNotification = DefaultRecordingPermissionDeniedNotification,
    StartRecordingAudioButton = DefaultStartRecordingAudioButton,
    TextareaComposer = DefaultTextareaComposer,
  } = useComponentContext();

  const [showRecordingPermissionDeniedNotification, setShowRecordingPermissionDeniedNotification] = useState(false);
  const closePermissionDeniedNotification = useCallback(() => {
    setShowRecordingPermissionDeniedNotification(false);
  }, []);

  const { attachments, isUploadEnabled, successfulUploadsCount, failedUploadsCount, availableUploadSlots } =
    useAttachmentManagerState();
  const {
    textComposer: { text },
    state,
    attachmentManager: { acceptedFiles, uploadFiles, maxNumberOfFilesPerMessage },
  } = useMessageComposer();
  const { quotedMessage } = useStateStore(state, messageComposerStateStoreSelector);
  const { validateFiles, handleValidationErrors } = useFileUploadValidator({
    maxSizeInBytes: MAX_FILE_SIZE_IN_BYTES,
    errorMessages: {
      fileSizeMin: (filename, min) => errors('fileSizeMin', { filename, min }),
      fileSizeMax: (filename, max) => errors('fileSizeMax', { filename, max }),
      fileTypeInvalid: (filename) => errors('fileTypeInvalid', { filename }),
    },
  });

  const multipleUploads = maxNumberOfFilesPerMessage > 1;
  const disableUpload = !isUploadEnabled || availableUploadSlots === 0;

  const accept = useMemo(
    () =>
      acceptedFiles.reduce<Record<string, Array<string>>>((acc, mediaType) => {
        const mediaTypeMap = { ...acc };
        mediaTypeMap[mediaType] ??= [];
        return mediaTypeMap;
      }, {}),
    [acceptedFiles]
  );

  const { getRootProps, isDragActive, isDragReject } = useDropzone({
    accept,
    disabled: disableUpload,
    multiple: multipleUploads,
    noClick: true,
    onDrop: (acceptedFiles) => onUploadFiles(acceptedFiles),
  });

  const onUploadFiles = (files: File[]) => {
    const results = validateFiles(files);
    handleValidationErrors(results);

    const validFiles = results.filter(({ isValid }) => isValid).map(({ file }) => file);
    if (!validFiles.length) return;
    uploadFiles(validFiles);
  };

  if (recordingController.recordingState) return <AudioRecorder />;

  const recordingEnabled = !!(recordingController.recorder && navigator.mediaDevices); // account for requirement on iOS as per this bug report: https://bugs.webkit.org/show_bug.cgi?id=252303
  const isRecording = !!recordingController.recordingState;

  return (
    <div {...getRootProps({ className: 'str-chat__message-input pb-4' })}>
      {recordingEnabled &&
        recordingController.permissionState === 'denied' &&
        showRecordingPermissionDeniedNotification && (
          <RecordingPermissionDeniedNotification
            onClose={closePermissionDeniedNotification}
            permissionName={RecordingPermission.MIC}
          />
        )}
      <LinkPreviewList />
      <QuotedMessagePreviewHeader />

      {isDragActive && (
        <div
          className={cn('str-chat__dropzone-container', {
            'str-chat__dropzone-container--not-accepted': isDragReject,
          })}
        >
          {!isDragReject && <p>{t('Drag your files here')}</p>}
          {isDragReject && <p>{t('Some of the files will not be accepted')}</p>}
        </div>
      )}

      {quotedMessage && (
        <div className="w-full py-2">{quotedMessage && <QuotedMessagePreview quotedMessage={quotedMessage} />}</div>
      )}

      <div className="str-chat__message-input-inner flex flex-row items-center gap-2">
        <FileUpload.Root
          accept={acceptedFiles?.join(',')}
          aria-label={t('aria/File upload')}
          data-testid="file-input"
          disabled={!isUploadEnabled || availableUploadSlots === 0}
          id="upload-button"
          multiple={multipleUploads}
          onChange={onUploadFiles}
          className="flex flex-row items-center h-[36px]"
        >
          <FileUpload.Trigger>
            <div className="relative cursor-pointer">
              <PlusIcon className="text-icon-brand w-[36px] h-[36px] mb-1" />
            </div>
          </FileUpload.Trigger>
        </FileUpload.Root>

        <div className="str-chat__message-textarea-container sticky bottom-0">
          {isUploadEnabled && !!(successfulUploadsCount + failedUploadsCount || attachments.length > 0) && (
            <AttachmentPreviewList />
          )}
          <div className="str-chat__message-textarea-with-emoji-picker">
            <TextareaComposer placeholder={messages('placeholder')} maxRows={5} />
            {EmojiPicker && <EmojiPicker />}
          </div>
        </div>
        {!hideSendButton &&
          (cooldownRemaining ? (
            <CooldownTimer cooldownInterval={cooldownRemaining} setCooldownRemaining={setCooldownRemaining} />
          ) : (
            <>
              <IconButton
                disabled={!hasSendableData}
                aria-label="send-message"
                variant="contained"
                size="sm"
                color="primary"
                shape="rounded"
                icon={PaperAirplaneIcon}
                onClick={handleSubmit}
              />
              {recordingEnabled && (
                <StartRecordingAudioButton
                  disabled={
                    isRecording ||
                    (!asyncMessagesMultiSendEnabled &&
                      attachments.some((a) => a.type === RecordingAttachmentType.VOICE_RECORDING))
                  }
                  onClick={() => {
                    recordingController.recorder?.start();
                    setShowRecordingPermissionDeniedNotification(true);
                  }}
                />
              )}
            </>
          ))}
      </div>
    </div>
  );
};
