import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import { MagnifyingGlassIcon } from '@shape-construction/arch-ui/src/Icons/outline';

export const PotentialChangesEmptyStateSelectionMode: React.FC = () => {
  const messages = useMessageGetter('controlCenter.commercialTracker.emptyStatePotentialChangesSelectionMode');
  return (
    <div className="flex flex-col bg-gray-100 items-center justify-center grow sm:grow">
      <div className="max-w-[640px] p-4 flex flex-col items-center justify-center gap-6">
        <div className="flex flex-col items-center justify-center gap-3">
          <MagnifyingGlassIcon className="h-12 w-12 text-neutral-subtle" />
          <div className="flex flex-col items-center justify-center gap-2">
            <span className="text-lg leading-6 font-medium text-neutral-bold">{messages('title')}</span>
            <span className="text-sm leading-5 font-normal text-neutral-subtle text-center">
              {messages('subtitle')}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
