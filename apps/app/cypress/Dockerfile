FROM cypress/browsers:node-22.18.0-chrome-139.0.7258.138-1-ff-142.0-edge-139.0.3405.102-1 AS base
RUN apt-get update; \
  apt-get install -y --no-install-recommends build-essential;
RUN corepack enable;

FROM base AS prepare
WORKDIR /app
COPY . .
RUN pnpm dlx turbo prune app --docker

FROM base AS runner
USER node
WORKDIR /e2e/src
COPY --from=prepare --chown=node:node /app/out/json/ .
COPY --from=prepare --chown=node:node /app/pnpm-lock.yaml /app/pnpm-workspace.yaml .
RUN pnpm config set store-dir /tmp/.pnpm-store
RUN pnpm install --frozen-lockfile
COPY --from=prepare --chown=node:node /app/out/full/ .

ENTRYPOINT [ "apps/app/cypress/entrypoint.sh" ]
CMD [ "--browser=chrome" ]
