{"name": "@shape-construction/utils", "version": "1.0.0", "description": "Shared utilities for our apps", "engines": {"node": "v22.18.0", "pnpm": "9.0.4"}, "scripts": {"compile": "tsc -p tsconfig.json", "lint": "biome check .", "lint:autofix": "biome check --write .", "format": "biome format --write", "test": "jest", "test:ci": "jest --ci --coverage"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@biomejs/biome": "2.0.6", "typescript": "5.8.3"}, "dependencies": {"dayjs": "^1.11.5", "dompurify": "3.2.6", "html-react-parser": "5.2.5", "jest": "29.4.3", "ts-jest": "^29.4.0"}}