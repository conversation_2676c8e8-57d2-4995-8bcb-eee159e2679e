import React, { useContext } from 'react';
import { userFactory } from '@shape-construction/api/factories/users';
import { render, screen } from 'tests/test-utils';
import { LocaleContext, LocaleProvider } from './LocaleProvider';
import { loadMessages } from './loadMessages';

jest.mock('./loadMessages', () => ({
  loadMessages: jest.fn(),
}));

const LOCALE_OPTIONS = [
  { value: 'en', label: 'English', subLabel: 'English (United States)' },
  { value: 'es-419', label: 'Español (Latinoamérica)', subLabel: 'Spanish (Latin America)' },
] as const;

const TestComponent = () => {
  const { locale } = useContext(LocaleContext);

  return (
    <div>
      <div data-testid="locale-status">Locale: {locale}</div>
      {LOCALE_OPTIONS.map((option) => (
        <button type="button" key={option.value}>
          {option.label}
        </button>
      ))}
    </div>
  );
};

describe('<LocaleProvider>', () => {
  beforeEach(() => {
    (loadMessages as jest.Mock).mockResolvedValue({
      default: { greeting: 'Hello' },
    });
  });

  it('initializes with default locale', async () => {
    const user = userFactory({ language: null });
    render(
      <LocaleProvider>
        <TestComponent />
      </LocaleProvider>,
      { user }
    );

    expect(screen.getByTestId('locale-status')).toHaveTextContent('Locale: en');
  });

  describe('when a message import fails', () => {
    it('falls back to default locale', async () => {
      (loadMessages as jest.Mock).mockRejectedValueOnce(new Error('Import failed'));

      render(
        <LocaleProvider>
          <TestComponent />
        </LocaleProvider>
      );

      expect(screen.getByTestId('locale-status')).toHaveTextContent('Locale: en');
    });
  });

  describe('when the user does not have a language saved', () => {
    it('sets the browser language as locale', async () => {
      const user = userFactory({ language: null });
      jest.spyOn(navigator, 'language', 'get').mockReturnValue('es-419');

      render(
        <LocaleProvider>
          <TestComponent />
        </LocaleProvider>,
        { user }
      );

      expect(await screen.findByTestId('locale-status')).toHaveTextContent('Locale: es-419');
    });

    describe('when the browser language is not supported', () => {
      it('falls back to default locale', async () => {
        const user = userFactory({ language: null });
        jest.spyOn(navigator, 'language', 'get').mockReturnValue('fr-FR');

        render(
          <LocaleProvider>
            <TestComponent />
          </LocaleProvider>,
          { user }
        );

        expect(await screen.findByTestId('locale-status')).toHaveTextContent('Locale: en');
      });
    });
  });

  describe('when the user has a language saved', () => {
    it('uses the saved language as locale', async () => {
      const user = userFactory({ language: 'es-419' });

      render(
        <LocaleProvider>
          <TestComponent />
        </LocaleProvider>,
        { user }
      );

      expect(await screen.findByTestId('locale-status')).toHaveTextContent('Locale: es-419');
    });
  });
});
